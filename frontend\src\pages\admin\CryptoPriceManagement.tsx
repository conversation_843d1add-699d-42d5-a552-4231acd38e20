import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Heading,
  VStack,
  HStack,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  Input,
  Select,
  Text,
  Badge,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
  Flex,
  IconButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  FormControl,
  FormLabel,
  NumberInput,
  NumberInputField,
  useDisclosure,
  Card,
  CardHeader,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Grid,
  GridItem,
  Divider
} from '@chakra-ui/react';
import { FaEdit, FaTrash, FaPlus, FaRefresh, FaChartBar } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { adminApiService } from '../../services/adminApi';

interface CryptoPriceEntry {
  _id: string;
  symbol: string;
  name: string;
  price: number;
  volume24h?: number;
  marketCap?: number;
  change24h?: number;
  changePercent24h?: number;
  source: 'binance' | 'coingecko' | 'internal';
  timestamp: string;
  createdAt: string;
  updatedAt: string;
}

interface Statistics {
  totalEntries: number;
  uniqueSymbolsCount: number;
  uniqueSymbols: string[];
  latestEntries: CryptoPriceEntry[];
  oldestEntry: CryptoPriceEntry | null;
  sourceStats: { _id: string; count: number }[];
}

const CryptoPriceManagement = () => {
  const { t } = useTranslation();
  const toast = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [prices, setPrices] = useState<CryptoPriceEntry[]>([]);
  const [statistics, setStatistics] = useState<Statistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [selectedPrice, setSelectedPrice] = useState<CryptoPriceEntry | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  // Pagination and filtering
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    symbol: '',
    source: '',
    sortBy: 'timestamp',
    sortOrder: 'desc'
  });

  // Form data
  const [formData, setFormData] = useState({
    symbol: '',
    name: '',
    price: 0,
    volume24h: 0,
    marketCap: 0,
    change24h: 0,
    changePercent24h: 0,
    source: 'internal' as 'binance' | 'coingecko' | 'internal'
  });

  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";

  useEffect(() => {
    fetchPrices();
    fetchStatistics();
  }, [currentPage, filters]);

  const fetchPrices = async () => {
    try {
      setLoading(true);
      const response = await adminApiService.getCryptoPrices({
        page: currentPage,
        limit: 20,
        ...filters
      });

      if (response.data.success) {
        setPrices(response.data.data.prices);
        setTotalPages(response.data.data.pagination.pages);
      }
    } catch (error) {
      console.error('Error fetching crypto prices:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch crypto prices',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await adminApiService.getCryptoPriceStatistics();
      if (response.data.success) {
        setStatistics(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      
      if (isEditing && selectedPrice) {
        await adminApiService.updateCryptoPrice(selectedPrice._id, formData);
        toast({
          title: 'Success',
          description: 'Price updated successfully',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      } else {
        await adminApiService.createCryptoPrice(formData);
        toast({
          title: 'Success',
          description: 'Price created successfully',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      }

      onClose();
      resetForm();
      fetchPrices();
      fetchStatistics();
    } catch (error) {
      console.error('Error saving price:', error);
      toast({
        title: 'Error',
        description: 'Failed to save price',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (price: CryptoPriceEntry) => {
    setSelectedPrice(price);
    setFormData({
      symbol: price.symbol,
      name: price.name,
      price: price.price,
      volume24h: price.volume24h || 0,
      marketCap: price.marketCap || 0,
      change24h: price.change24h || 0,
      changePercent24h: price.changePercent24h || 0,
      source: price.source
    });
    setIsEditing(true);
    onOpen();
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this price entry?')) {
      return;
    }

    try {
      await adminApiService.deleteCryptoPrice(id);
      toast({
        title: 'Success',
        description: 'Price deleted successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      fetchPrices();
      fetchStatistics();
    } catch (error) {
      console.error('Error deleting price:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete price',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const resetForm = () => {
    setFormData({
      symbol: '',
      name: '',
      price: 0,
      volume24h: 0,
      marketCap: 0,
      change24h: 0,
      changePercent24h: 0,
      source: 'internal'
    });
    setSelectedPrice(null);
    setIsEditing(false);
  };

  const handleAddNew = () => {
    resetForm();
    onOpen();
  };

  const formatNumber = (num: number | undefined) => {
    if (num === undefined || num === null) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 8
    }).format(num);
  };

  const formatCurrency = (num: number | undefined) => {
    if (num === undefined || num === null) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 8
    }).format(num);
  };

  const getSourceColor = (source: string) => {
    switch (source) {
      case 'binance': return 'yellow';
      case 'coingecko': return 'green';
      case 'internal': return 'blue';
      default: return 'gray';
    }
  };

  return (
    <Box>
      <Heading size="lg" color="#F0B90B" mb={6}>Crypto Price Management</Heading>

      {/* Statistics Cards */}
      {statistics && (
        <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(4, 1fr)" }} gap={6} mb={6}>
          <GridItem>
            <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
              <CardBody>
                <Stat>
                  <StatLabel color={secondaryTextColor}>Total Entries</StatLabel>
                  <StatNumber color={textColor}>{statistics.totalEntries.toLocaleString()}</StatNumber>
                </Stat>
              </CardBody>
            </Card>
          </GridItem>
          <GridItem>
            <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
              <CardBody>
                <Stat>
                  <StatLabel color={secondaryTextColor}>Unique Symbols</StatLabel>
                  <StatNumber color={textColor}>{statistics.uniqueSymbolsCount}</StatNumber>
                </Stat>
              </CardBody>
            </Card>
          </GridItem>
          <GridItem>
            <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
              <CardBody>
                <Stat>
                  <StatLabel color={secondaryTextColor}>Data Sources</StatLabel>
                  <StatNumber color={textColor}>{statistics.sourceStats.length}</StatNumber>
                  <StatHelpText color={secondaryTextColor}>
                    {statistics.sourceStats.map(s => s._id).join(', ')}
                  </StatHelpText>
                </Stat>
              </CardBody>
            </Card>
          </GridItem>
          <GridItem>
            <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
              <CardBody>
                <Stat>
                  <StatLabel color={secondaryTextColor}>Latest Update</StatLabel>
                  <StatNumber color={textColor} fontSize="sm">
                    {statistics.latestEntries[0] ? 
                      new Date(statistics.latestEntries[0].timestamp).toLocaleString() : 'N/A'}
                  </StatNumber>
                </Stat>
              </CardBody>
            </Card>
          </GridItem>
        </Grid>
      )}

      {/* Controls */}
      <Card bg={bgColor} borderColor={borderColor} borderWidth="1px" mb={6}>
        <CardHeader>
          <Flex justify="space-between" align="center">
            <Heading size="md" color={textColor}>Price Data</Heading>
            <HStack>
              <Button
                leftIcon={<FaPlus />}
                colorScheme="yellow"
                onClick={handleAddNew}
              >
                Add Price
              </Button>
              <Button
                leftIcon={<FaRefresh />}
                variant="outline"
                borderColor={borderColor}
                color={textColor}
                onClick={fetchPrices}
              >
                Refresh
              </Button>
            </HStack>
          </Flex>
        </CardHeader>
        <Divider borderColor={borderColor} />
        <CardBody>
          {/* Filters */}
          <HStack spacing={4} mb={4}>
            <Input
              placeholder="Filter by symbol"
              value={filters.symbol}
              onChange={(e) => setFilters({ ...filters, symbol: e.target.value })}
              bg={cardBgColor}
              borderColor={borderColor}
              color={textColor}
              maxW="200px"
            />
            <Select
              value={filters.source}
              onChange={(e) => setFilters({ ...filters, source: e.target.value })}
              bg={cardBgColor}
              borderColor={borderColor}
              color={textColor}
              maxW="150px"
            >
              <option value="">All Sources</option>
              <option value="binance">Binance</option>
              <option value="coingecko">CoinGecko</option>
              <option value="internal">Internal</option>
            </Select>
            <Select
              value={filters.sortBy}
              onChange={(e) => setFilters({ ...filters, sortBy: e.target.value })}
              bg={cardBgColor}
              borderColor={borderColor}
              color={textColor}
              maxW="150px"
            >
              <option value="timestamp">Timestamp</option>
              <option value="symbol">Symbol</option>
              <option value="price">Price</option>
              <option value="source">Source</option>
            </Select>
            <Select
              value={filters.sortOrder}
              onChange={(e) => setFilters({ ...filters, sortOrder: e.target.value })}
              bg={cardBgColor}
              borderColor={borderColor}
              color={textColor}
              maxW="100px"
            >
              <option value="desc">Desc</option>
              <option value="asc">Asc</option>
            </Select>
          </HStack>

          {/* Table */}
          {loading ? (
            <Flex justify="center" py={8}>
              <Spinner size="xl" color="#F0B90B" />
            </Flex>
          ) : (
            <Box overflowX="auto">
              <Table variant="simple">
                <Thead>
                  <Tr>
                    <Th color={secondaryTextColor}>Symbol</Th>
                    <Th color={secondaryTextColor}>Name</Th>
                    <Th color={secondaryTextColor}>Price</Th>
                    <Th color={secondaryTextColor}>24h Change</Th>
                    <Th color={secondaryTextColor}>Source</Th>
                    <Th color={secondaryTextColor}>Timestamp</Th>
                    <Th color={secondaryTextColor}>Actions</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {prices.map((price) => (
                    <Tr key={price._id}>
                      <Td color={textColor} fontWeight="bold">{price.symbol}</Td>
                      <Td color={textColor}>{price.name}</Td>
                      <Td color={textColor}>{formatCurrency(price.price)}</Td>
                      <Td>
                        {price.changePercent24h !== undefined ? (
                          <Text color={price.changePercent24h >= 0 ? 'green.400' : 'red.400'}>
                            {price.changePercent24h >= 0 ? '+' : ''}{price.changePercent24h.toFixed(2)}%
                          </Text>
                        ) : (
                          <Text color={secondaryTextColor}>N/A</Text>
                        )}
                      </Td>
                      <Td>
                        <Badge colorScheme={getSourceColor(price.source)}>
                          {price.source}
                        </Badge>
                      </Td>
                      <Td color={secondaryTextColor} fontSize="sm">
                        {new Date(price.timestamp).toLocaleString()}
                      </Td>
                      <Td>
                        <HStack>
                          <IconButton
                            aria-label="Edit"
                            icon={<FaEdit />}
                            size="sm"
                            colorScheme="blue"
                            variant="ghost"
                            onClick={() => handleEdit(price)}
                          />
                          <IconButton
                            aria-label="Delete"
                            icon={<FaTrash />}
                            size="sm"
                            colorScheme="red"
                            variant="ghost"
                            onClick={() => handleDelete(price._id)}
                          />
                        </HStack>
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            </Box>
          )}

          {/* Pagination */}
          <Flex justify="space-between" align="center" mt={4}>
            <Text color={secondaryTextColor}>
              Page {currentPage} of {totalPages}
            </Text>
            <HStack>
              <Button
                size="sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                isDisabled={currentPage === 1}
                bg={cardBgColor}
                borderColor={borderColor}
                color={textColor}
              >
                Previous
              </Button>
              <Button
                size="sm"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                isDisabled={currentPage === totalPages}
                bg={cardBgColor}
                borderColor={borderColor}
                color={textColor}
              >
                Next
              </Button>
            </HStack>
          </Flex>
        </CardBody>
      </Card>

      {/* Add/Edit Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalOverlay />
        <ModalContent bg={bgColor} borderColor={borderColor}>
          <ModalHeader color={textColor}>
            {isEditing ? 'Edit Price Entry' : 'Add New Price Entry'}
          </ModalHeader>
          <ModalCloseButton color={textColor} />
          <ModalBody>
            <VStack spacing={4}>
              <FormControl>
                <FormLabel color={secondaryTextColor}>Symbol</FormLabel>
                <Input
                  value={formData.symbol}
                  onChange={(e) => setFormData({ ...formData, symbol: e.target.value.toUpperCase() })}
                  bg={cardBgColor}
                  borderColor={borderColor}
                  color={textColor}
                  placeholder="BTC"
                />
              </FormControl>

              <FormControl>
                <FormLabel color={secondaryTextColor}>Name</FormLabel>
                <Input
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  bg={cardBgColor}
                  borderColor={borderColor}
                  color={textColor}
                  placeholder="Bitcoin"
                />
              </FormControl>

              <FormControl>
                <FormLabel color={secondaryTextColor}>Price (USD)</FormLabel>
                <NumberInput
                  value={formData.price}
                  onChange={(_, value) => setFormData({ ...formData, price: value || 0 })}
                  min={0}
                  precision={8}
                >
                  <NumberInputField
                    bg={cardBgColor}
                    borderColor={borderColor}
                    color={textColor}
                  />
                </NumberInput>
              </FormControl>

              <FormControl>
                <FormLabel color={secondaryTextColor}>24h Volume</FormLabel>
                <NumberInput
                  value={formData.volume24h}
                  onChange={(_, value) => setFormData({ ...formData, volume24h: value || 0 })}
                  min={0}
                >
                  <NumberInputField
                    bg={cardBgColor}
                    borderColor={borderColor}
                    color={textColor}
                  />
                </NumberInput>
              </FormControl>

              <FormControl>
                <FormLabel color={secondaryTextColor}>Market Cap</FormLabel>
                <NumberInput
                  value={formData.marketCap}
                  onChange={(_, value) => setFormData({ ...formData, marketCap: value || 0 })}
                  min={0}
                >
                  <NumberInputField
                    bg={cardBgColor}
                    borderColor={borderColor}
                    color={textColor}
                  />
                </NumberInput>
              </FormControl>

              <FormControl>
                <FormLabel color={secondaryTextColor}>24h Change (%)</FormLabel>
                <NumberInput
                  value={formData.changePercent24h}
                  onChange={(_, value) => setFormData({ ...formData, changePercent24h: value || 0 })}
                  precision={2}
                >
                  <NumberInputField
                    bg={cardBgColor}
                    borderColor={borderColor}
                    color={textColor}
                  />
                </NumberInput>
              </FormControl>

              <FormControl>
                <FormLabel color={secondaryTextColor}>Source</FormLabel>
                <Select
                  value={formData.source}
                  onChange={(e) => setFormData({ ...formData, source: e.target.value as any })}
                  bg={cardBgColor}
                  borderColor={borderColor}
                  color={textColor}
                >
                  <option value="internal">Internal</option>
                  <option value="binance">Binance</option>
                  <option value="coingecko">CoinGecko</option>
                </Select>
              </FormControl>
            </VStack>
          </ModalBody>

          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose} color={textColor}>
              Cancel
            </Button>
            <Button
              colorScheme="yellow"
              onClick={handleSubmit}
              isLoading={submitting}
            >
              {isEditing ? 'Update' : 'Create'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default CryptoPriceManagement;
