import { useState, useEffect, Suspense, lazy } from 'react';
import {
  Box,
  Flex,
  Text,
  Button,
  Stack,
  useDisclosure,
  IconButton,
  HStack,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Avatar,
  Divider,
  Tooltip,
  <PERSON>er,
  <PERSON>er<PERSON><PERSON>,
  <PERSON>er<PERSON>eader,
  DrawerOverlay,
  Drawer<PERSON>ontent,
  DrawerCloseButton,
  VStack,
  Spinner
} from '@chakra-ui/react';
import { HamburgerIcon, ChevronDownIcon } from '@chakra-ui/icons';
import { Link as RouterLink, useLocation, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import useAuth from '../hooks/useAuth';
import {
  FaUsers,
  FaMoneyBillWave,
  FaExchangeAlt,
  FaChartLine,
  FaHome,
  FaCog,
  FaSignOutAlt,
  FaUserCog,
  FaDatabase,
  FaWallet,
  FaHandHoldingUsd,
  FaUserFriends,
  FaEdit,
  FaServer,
  <PERSON>aCoi<PERSON>,
  <PERSON>a<PERSON>ell,
  FaChartBar
} from 'react-icons/fa';

// Lazy load notification components
const RealTimeNotifications = lazy(() => import('./admin/RealTimeNotifications'));

// Fallback for lazy loaded components
const NotificationFallback = () => (
  <Box display="flex" alignItems="center" justifyContent="center" h="24px" w="24px">
    <Spinner size="sm" color="#F0B90B" />
  </Box>
);

const AdminNavbar = () => {
  const { t } = useTranslation('translation');
  const { isOpen, onClose, onOpen } = useDisclosure();
  const { user, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Format date and time
  const formattedDate = new Intl.DateTimeFormat('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(currentTime);

  const formattedTime = new Intl.DateTimeFormat('en-US', {
    hour: '2-digit',
    minute: '2-digit',
  }).format(currentTime);

  // Navigation items for admin panel - optimized and organized
  const navItems = [
    { name: t('admin.nav.dashboard', 'Dashboard'), path: '/admin', icon: FaChartLine, description: t('admin.descriptions.dashboard', 'Overview and statistics') },
    { name: t('admin.nav.users', 'Users'), path: '/admin/users', icon: FaUsers, description: t('admin.descriptions.users', 'User management') },
    { name: t('admin.nav.transactions', 'Transactions'), path: '/admin/transactions', icon: FaExchangeAlt, description: t('admin.descriptions.transactions', 'All transaction history') },
    { name: t('admin.nav.deposits', 'Deposits'), path: '/admin/deposits', icon: FaWallet, description: t('admin.descriptions.deposits', 'Deposit operations') },
    { name: t('admin.nav.withdrawals', 'Withdrawals'), path: '/admin/withdrawals', icon: FaHandHoldingUsd, description: t('admin.descriptions.withdrawals', 'Withdrawal operations') },
    { name: t('admin.nav.referrals', 'Referrals'), path: '/admin/referrals', icon: FaUserFriends, description: t('admin.descriptions.referrals', 'Referral system management') },
    { name: t('admin.nav.cryptoAddresses', 'Crypto Addresses'), path: '/admin/crypto-addresses', icon: FaCoins, description: t('admin.descriptions.cryptoAddresses', 'Manage cryptocurrency deposit addresses') },
    { name: 'Crypto Prices', path: '/admin/crypto-prices', icon: FaChartBar, description: 'Manage cryptocurrency price data' },
    { name: t('admin.nav.siteManagement', 'Site Management'), path: '/admin/site-management', icon: FaEdit, description: t('admin.descriptions.siteManagement', 'Homepage, profile and cryptocurrency management') },
    { name: t('admin.nav.systemManagement', 'System Management'), path: '/admin/settings', icon: FaServer, description: t('admin.descriptions.systemManagement', 'System configuration and crypto addresses') }
  ];

  // Check if the current path is active
  const isActive = (path: string) => {
    if (path === '/admin') {
      return location.pathname === '/admin';
    }
    return location.pathname.startsWith(path);
  };

  // Handle logout with confirmation
  const handleLogout = () => {
    if (window.confirm(t('common.logoutConfirm', 'Are you sure you want to log out?'))) {
      logout();
      navigate('/');
    }
  };

  return (
    <Box>
      {/* Main Navbar */}
      <Flex
        bg="#0B0E11"
        color="#EAECEF"
        minH={'60px'}
        py={{ base: 2 }}
        px={{ base: 4 }}
        borderBottom={1}
        borderStyle={'solid'}
        borderColor="#1E2329"
        align={'center'}
        justify={'space-between'}
        boxShadow="0 2px 10px rgba(0, 0, 0, 0.3)"
      >
        {/* Mobile menu button */}
        <Flex
          flex={{ base: 1, md: 'auto' }}
          ml={{ base: -2 }}
          display={{ base: 'flex', md: 'none' }}
        >
          <IconButton
            onClick={onOpen}
            icon={<HamburgerIcon w={5} h={5} />}
            variant={'ghost'}
            aria-label={'Toggle Navigation'}
            _hover={{ bg: 'rgba(240, 185, 11, 0.1)' }}
          />
        </Flex>

        {/* Logo and Navigation */}
        <Flex flex={{ base: 1 }} justify={{ base: 'center', md: 'start' }} align="center">
          {/* Logo */}
          <Flex align="center">
            <Text
              as={RouterLink}
              to="/admin"
              textAlign={{ base: 'center', md: 'left' }}
              fontFamily={'heading'}
              color="#F0B90B"
              fontWeight="bold"
              fontSize="xl"
              display="flex"
              alignItems="center"
              _hover={{ textDecoration: 'none' }}
            >
              <Box
                w="28px"
                h="28px"
                bg="#F0B90B"
                borderRadius="md"
                mr={2}
                display="flex"
                alignItems="center"
                justifyContent="center"
                boxShadow="0 0 10px rgba(240, 185, 11, 0.5)"
              >
                <Box w="18px" h="18px" bg="#0B0E11" borderRadius="sm" />
              </Box>
              <Text>{t('admin.title', 'Admin Panel')}</Text>
            </Text>
          </Flex>

          {/* Desktop Navigation */}
          <Box
            as={'nav'}
            display={{ base: 'none', lg: 'block' }}
            ml={8}
            flex="1"
            maxW="calc(100vw - 600px)"
            overflowX="auto"
            css={{
              '&::-webkit-scrollbar': {
                height: '4px',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: 'rgba(240, 185, 11, 0.4)',
                borderRadius: '2px',
              },
              '&::-webkit-scrollbar-track': {
                backgroundColor: 'rgba(0, 0, 0, 0.1)',
              },
            }}
          >
            <HStack spacing={0} wrap="nowrap" minW="fit-content">
              {navItems.map((item) => (
                <Tooltip
                  key={item.path}
                  label={item.description}
                  placement="bottom"
                  bg="#1E2329"
                  color="#EAECEF"
                  hasArrow
                  fontSize="sm"
                >
                  <Button
                    as={RouterLink}
                    to={item.path}
                    variant="ghost"
                    size="sm"
                    leftIcon={<Box as={item.icon} boxSize="14px" />}
                    color={isActive(item.path) ? "#F0B90B" : "#EAECEF"}
                    fontWeight={isActive(item.path) ? 600 : 500}
                    borderBottom={isActive(item.path) ? "2px solid #F0B90B" : "none"}
                    borderRadius="0"
                    _hover={{
                      textDecoration: 'none',
                      color: '#F0B90B',
                      bg: 'rgba(240, 185, 11, 0.05)'
                    }}
                    px={3}
                    py={4}
                    minW="auto"
                    fontSize="sm"
                    whiteSpace="nowrap"
                  >
                    <Text display={{ base: 'none', xl: 'block' }}>{item.name}</Text>
                    <Text display={{ base: 'block', xl: 'none' }} fontSize="xs">
                      {item.name.split(' ')[0]}
                    </Text>
                  </Button>
                </Tooltip>
              ))}
            </HStack>
          </Box>
        </Flex>

        {/* Right side - User menu and back button */}
        <Stack
          flex={{ base: 1, md: 0 }}
          justify={'flex-end'}
          direction={'row'}
          spacing={4}
          align="center"
        >
          {/* Admin Notifications */}
          <Box display={{ base: 'none', md: 'block' }}>
            <Suspense fallback={<NotificationFallback />}>
              <RealTimeNotifications />
            </Suspense>
          </Box>

          {/* Date and Time */}
          <Box display={{ base: 'none', md: 'block' }}>
            <Text fontSize="xs" color="#848E9C" textAlign="right">{formattedDate}</Text>
            <Text fontSize="sm" color="#EAECEF" fontWeight="500">{formattedTime}</Text>
          </Box>

          {/* Back to Site Button */}
          <Button
            as={RouterLink}
            to="/"
            variant="outline"
            colorScheme="yellow"
            size="sm"
            leftIcon={<Box as={FaHome} />}
            _hover={{
              bg: 'rgba(240, 185, 11, 0.1)',
              transform: 'translateY(-2px)',
              boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)'
            }}
            transition="all 0.2s"
          >
{t('admin.backToSite', 'Back to Site')}
          </Button>

          {/* User Menu */}
          <Menu>
            <MenuButton
              as={Button}
              variant={'ghost'}
              cursor={'pointer'}
              minW={0}
              rightIcon={<ChevronDownIcon color="#848E9C" />}
              color="#EAECEF"
              _hover={{
                bg: 'rgba(240, 185, 11, 0.05)',
              }}
              borderRadius="sm"
            >
              <HStack>
                <Avatar
                  size={'sm'}
                  name={`${user?.firstName} ${user?.lastName}`}
                  bg="#F0B90B"
                  color="#0B0E11"
                  mr={1}
                  boxShadow="0 0 0 2px rgba(240, 185, 11, 0.3)"
                />
                <Box display={{ base: 'none', md: 'block' }}>
                  <Text fontSize="sm" fontWeight="500">{user?.firstName} {user?.lastName}</Text>
                  <Text fontSize="xs" color="#848E9C">{t('admin.role', 'Administrator')}</Text>
                </Box>
              </HStack>
            </MenuButton>
            <MenuList bg="#1E2329" borderColor="#2B3139" boxShadow="xl">
              <MenuItem
                as={RouterLink}
                to="/profile"
                bg="#1E2329"
                _hover={{ bg: '#2B3139' }}
                icon={<Box as={FaUserCog} color="#F0B90B" />}
              >
{t('profile', 'Profile')}
              </MenuItem>
              <MenuItem
                as={RouterLink}
                to="/admin/settings"
                bg="#1E2329"
                _hover={{ bg: '#2B3139' }}
                icon={<Box as={FaCog} color="#F0B90B" />}
              >
{t('admin.settings', 'Admin Settings')}
              </MenuItem>
              <Divider borderColor="#2B3139" />
              <MenuItem
                onClick={handleLogout}
                bg="#1E2329"
                _hover={{ bg: '#2B3139', color: 'red.400' }}
                icon={<Box as={FaSignOutAlt} color="red.400" />}
              >
{t('common.logout', 'Logout')}
              </MenuItem>
            </MenuList>
          </Menu>
        </Stack>
      </Flex>

      {/* Mobile Navigation Drawer */}
      <Drawer isOpen={isOpen} placement="left" onClose={onClose} size="sm">
        <DrawerOverlay />
        <DrawerContent bg="#0B0E11" color="#EAECEF" maxW="280px">
          <DrawerCloseButton color="#F0B90B" size="lg" />
          <DrawerHeader borderBottomWidth="1px" borderColor="#1E2329" py={4}>
            <Flex align="center">
              <Box
                w="28px"
                h="28px"
                bg="#F0B90B"
                borderRadius="md"
                mr={3}
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                <Box w="18px" h="18px" bg="#0B0E11" borderRadius="sm" />
              </Box>
              <Text fontSize="lg" fontWeight="bold">
                {t('admin.title', 'Admin Panel')}
              </Text>
            </Flex>
          </DrawerHeader>
          <DrawerBody p={0}>
            <VStack align="stretch" spacing={1} py={2}>
              {/* Mobile Admin Notifications */}
              <Box p={4} borderBottom="1px solid #1E2329">
                <Suspense fallback={<NotificationFallback />}>
                  <RealTimeNotifications />
                </Suspense>
              </Box>

              {navItems.map((item) => (
                <Button
                  key={item.path}
                  as={RouterLink}
                  to={item.path}
                  variant="ghost"
                  justifyContent="flex-start"
                  size="lg"
                  py={6}
                  px={4}
                  leftIcon={<Box as={item.icon} color={isActive(item.path) ? "#F0B90B" : "#848E9C"} />}
                  bg={isActive(item.path) ? "rgba(240, 185, 11, 0.1)" : "transparent"}
                  color={isActive(item.path) ? "#F0B90B" : "#EAECEF"}
                  borderRadius={0}
                  borderLeft={isActive(item.path) ? "4px solid #F0B90B" : "4px solid transparent"}
                  _hover={{
                    bg: "rgba(240, 185, 11, 0.05)",
                    color: "#F0B90B"
                  }}
                  onClick={onClose}
                >
                  {item.name}
                </Button>
              ))}
              <Divider borderColor="#1E2329" />
              <Button
                as={RouterLink}
                to="/"
                variant="ghost"
                justifyContent="flex-start"
                leftIcon={<Box as={FaHome} color="#848E9C" />}
                borderRadius={0}
                _hover={{
                  bg: "rgba(240, 185, 11, 0.05)",
                  color: "#F0B90B"
                }}
                py={6}
                onClick={onClose}
              >
{t('admin.backToSite', 'Back to Site')}
              </Button>
              <Button
                variant="ghost"
                justifyContent="flex-start"
                leftIcon={<Box as={FaSignOutAlt} color="red.400" />}
                borderRadius={0}
                _hover={{
                  bg: "rgba(255, 0, 0, 0.1)",
                  color: "red.400"
                }}
                py={6}
                onClick={handleLogout}
              >
{t('common.logout', 'Logout')}
              </Button>
            </VStack>
          </DrawerBody>
        </DrawerContent>
      </Drawer>
    </Box>
  );
};

export default AdminNavbar;
