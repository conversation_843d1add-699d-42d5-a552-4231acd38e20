import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>lay,
  <PERSON>dal<PERSON>ontent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  HStack,
  Text,
  Box,
  Flex,
  useToast,
  Select,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Progress,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Divider,
  Badge,
  Icon,
  Radio,
  RadioGroup,
  Stack,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription
} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { FaInfoCircle } from 'react-icons/fa';

import useWallet from '../../hooks/useWallet';
import NetworkSelector from '../common/NetworkSelector';
import { CRYPTO_NETWORKS, getDefaultNetwork, NetworkOption } from '../../utils/cryptoNetworks';
import { investmentBalanceService, InvestmentBalance } from '../../services/investmentBalanceService';

// Phase 3: Interest-Only Withdrawal System interfaces
interface WithdrawableBalance {
  asset: string;
  principalAmount: number;
  interestAmount: number;
  commissionAmount: number;
  totalWithdrawable: number;
  principalLocked: boolean;
  principalLockUntil: Date | null;
  daysUntilUnlock: number;
  meetsMinimumThreshold: boolean;
  minimumThreshold: number;
}

interface WithdrawalEligibility {
  eligible: boolean;
  reason: string;
  details: {
    requestedAmount: number;
    availableAmount: number;
    minimumRequired: number;
    principalLocked: boolean;
    interestOnly: boolean;
  };
}

interface PrincipalLockStatus {
  packageId: string;
  locked: boolean;
  unlockDate: Date | null;
  daysRemaining: number;
  activatedAt: Date;
  lockDuration: number;
}

interface ThreeStepWithdrawModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialCrypto?: string; // Loại tiền tệ được chọn từ trang Home
  initialWithdrawalType?: 'interest' | 'commission';
  initialInterestAmount?: number; // Specific interest earnings amount from investment cards
}

const ThreeStepWithdrawModal: React.FC<ThreeStepWithdrawModalProps> = ({
  isOpen,
  onClose,
  initialCrypto,
  initialWithdrawalType = 'interest',
  initialInterestAmount
}) => {
  const { t } = useTranslation();
  const toast = useToast();

  const { wallet, loading, fetchWallet } = useWallet();

  // State variables
  const [activeStep, setActiveStep] = useState(0);
  const [selectedCrypto, setSelectedCrypto] = useState(initialCrypto ? mapCryptoNameToSymbol(initialCrypto) : 'BTC');
  const [amount, setAmount] = useState('');
  const [walletAddress, setWalletAddress] = useState('');
  const [memo, setMemo] = useState('');
  const [withdrawalType, setWithdrawalType] = useState(initialWithdrawalType);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedNetwork, setSelectedNetwork] = useState<string>('');
  const [networkOptions, setNetworkOptions] = useState<NetworkOption[]>([]);
  const [currentNetworkDetails, setCurrentNetworkDetails] = useState<NetworkOption | undefined>();
  const [investmentBalances, setInvestmentBalances] = useState<InvestmentBalance[]>([]);
  const [loadingBalances, setLoadingBalances] = useState(false);

  // Phase 3: Interest-Only Withdrawal System state
  const [withdrawableBalance, setWithdrawableBalance] = useState<WithdrawableBalance | null>(null);
  const [loadingWithdrawableBalance, setLoadingWithdrawableBalance] = useState(false);
  const [principalLockStatus, setPrincipalLockStatus] = useState<PrincipalLockStatus | null>(null);
  const [withdrawalValidationError, setWithdrawalValidationError] = useState<string | null>(null);
  const [phase3ValidationEnabled, setPhase3ValidationEnabled] = useState(true);

  // Hàm chuyển đổi tên tiền tệ từ trang Home sang mã tiền tệ
  function mapCryptoNameToSymbol(cryptoName: string): string {
    // Nếu đã là symbol (như BTC, ETH), trả về luôn
    const upperCrypto = cryptoName.toUpperCase();
    if (['BTC', 'ETH', 'USDT', 'DOGE', 'TRX', 'BNB'].includes(upperCrypto)) {
      return upperCrypto;
    }

    // Nếu là tên đầy đủ, chuyển đổi
    const cryptoMap: Record<string, string> = {
      'bitcoin': 'BTC',
      'ethereum': 'ETH',
      'tether': 'USDT',
      'dogecoin': 'DOGE',
      'tron': 'TRX',
      'bnb': 'BNB'
    };
    return cryptoMap[cryptoName.toLowerCase()] || 'BTC';
  }

  // Phase 3: Fetch withdrawable balance using new API
  const fetchWithdrawableBalance = async (asset: string) => {
    if (!phase3ValidationEnabled) return;

    setLoadingWithdrawableBalance(true);
    setWithdrawalValidationError(null);

    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/wallets/withdrawable-balance/${asset}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch withdrawable balance: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.status === 'success' && data.data.withdrawableBalance) {
        setWithdrawableBalance(data.data.withdrawableBalance);
        console.log('Phase 3 withdrawable balance loaded:', data.data.withdrawableBalance);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error fetching withdrawable balance:', error);
      setWithdrawalValidationError(error instanceof Error ? error.message : 'Failed to load withdrawal data');
      // Fallback to legacy system
      setPhase3ValidationEnabled(false);
    } finally {
      setLoadingWithdrawableBalance(false);
    }
  };

  // Phase 3: Validate withdrawal eligibility
  const validateWithdrawalEligibility = async (asset: string, amount: number): Promise<WithdrawalEligibility | null> => {
    if (!phase3ValidationEnabled) return null;

    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/wallets/withdraw`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          asset,
          amount,
          address: 'validation-check', // Dummy address for validation
          withdrawalType: withdrawalType,
          network: selectedNetwork,
          validateOnly: true // Flag to indicate this is validation only
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        // Extract validation error details
        if (data.withdrawalValidation) {
          return {
            eligible: false,
            reason: data.message || 'Withdrawal validation failed',
            details: data.withdrawalValidation
          };
        }
        throw new Error(data.message || 'Validation failed');
      }

      return {
        eligible: true,
        reason: 'Withdrawal eligible',
        details: {
          requestedAmount: amount,
          availableAmount: withdrawableBalance?.totalWithdrawable || 0,
          minimumRequired: withdrawableBalance?.minimumThreshold || 50,
          principalLocked: withdrawableBalance?.principalLocked || false,
          interestOnly: withdrawableBalance?.principalLocked || false
        }
      };
    } catch (error) {
      console.error('Error validating withdrawal eligibility:', error);
      return null;
    }
  };

  // Initialize network options when cryptocurrency changes
  useEffect(() => {
    const networks = CRYPTO_NETWORKS[selectedCrypto] || [];
    setNetworkOptions(networks);

    const defaultNetwork = getDefaultNetwork(selectedCrypto);
    if (defaultNetwork) {
      setSelectedNetwork(defaultNetwork.id);
      setCurrentNetworkDetails(defaultNetwork);
    }
  }, [selectedCrypto]);

  // Update current network details when selected network changes
  useEffect(() => {
    if (selectedNetwork && networkOptions.length > 0) {
      const networkDetails = networkOptions.find(network => network.id === selectedNetwork);
      setCurrentNetworkDetails(networkDetails);
    }
  }, [selectedNetwork, networkOptions]);

  // Reset amount when crypto or withdrawal type changes to avoid invalid amounts
  useEffect(() => {
    setAmount('');
  }, [selectedCrypto, withdrawalType]);

  // Phase 3: Load withdrawable balance when crypto changes or modal opens
  useEffect(() => {
    if (selectedCrypto && phase3ValidationEnabled && isOpen) {
      console.log('🔄 ThreeStepWithdrawModal: Fetching withdrawable balance for:', selectedCrypto);
      fetchWithdrawableBalance(selectedCrypto);
    }
  }, [selectedCrypto, phase3ValidationEnabled, isOpen]);

  // Phase 3: Real-time validation when amount changes
  useEffect(() => {
    const validateAmount = async () => {
      if (amount && parseFloat(amount) > 0 && selectedCrypto && phase3ValidationEnabled) {
        const validation = await validateWithdrawalEligibility(selectedCrypto, parseFloat(amount));
        if (validation && !validation.eligible) {
          setWithdrawalValidationError(validation.reason);
        } else {
          setWithdrawalValidationError(null);
        }
      } else {
        setWithdrawalValidationError(null);
      }
    };

    const timeoutId = setTimeout(validateAmount, 500); // Debounce validation
    return () => clearTimeout(timeoutId);
  }, [amount, selectedCrypto, withdrawalType, selectedNetwork, phase3ValidationEnabled]);

  // Load investment balances when modal opens
  useEffect(() => {
    const loadInvestmentBalances = async () => {
      if (isOpen) {
        setLoadingBalances(true);
        try {
          const balances = await investmentBalanceService.getInvestmentBalances();
          setInvestmentBalances(balances);
        } catch (error) {
          console.error('Error loading investment balances:', error);
          setInvestmentBalances([]);
        } finally {
          setLoadingBalances(false);
        }
      }
    };

    loadInvestmentBalances();
  }, [isOpen]);

  // CRITICAL: WebSocket listener for real-time balance updates
  useEffect(() => {
    if (!isOpen) return;

    const handleWalletUpdate = (data: any) => {
      console.log('Withdrawal modal received wallet update:', data);

      // Refresh wallet data to get latest balances
      if (fetchWallet) {
        fetchWallet();
      }

      // Also refresh investment balances
      const refreshInvestmentBalances = async () => {
        try {
          const balances = await investmentBalanceService.getInvestmentBalances();
          setInvestmentBalances(balances);
          console.log('Refreshed investment balances after wallet update:', balances);
        } catch (error) {
          console.error('Error refreshing investment balances:', error);
        }
      };

      refreshInvestmentBalances();
    };

    const handleBalanceUpdate = (data: any) => {
      console.log('Withdrawal modal received balance update:', data);

      // Refresh both wallet and investment balances
      if (fetchWallet) {
        fetchWallet();
      }

      // Refresh investment balances
      const refreshInvestmentBalances = async () => {
        try {
          const balances = await investmentBalanceService.getInvestmentBalances();
          setInvestmentBalances(balances);
          console.log('Refreshed investment balances after balance update:', balances);
        } catch (error) {
          console.error('Error refreshing investment balances:', error);
        }
      };

      refreshInvestmentBalances();
    };

    // Import WebSocket hook dynamically to avoid circular dependencies
    import('../../hooks/useWebSocket').then(({ default: useWebSocket }) => {
      const { subscribe, unsubscribe } = useWebSocket();

      // Subscribe to wallet and balance updates
      subscribe('wallet_update', handleWalletUpdate);
      subscribe('wallet_balance_updated', handleBalanceUpdate);

      // Cleanup subscriptions when modal closes
      return () => {
        unsubscribe('wallet_update', handleWalletUpdate);
        unsubscribe('wallet_balance_updated', handleBalanceUpdate);
      };
    }).catch(error => {
      console.error('Error setting up WebSocket subscriptions:', error);
    });
  }, [isOpen, fetchWallet]);

  // Update selectedCrypto and withdrawalType when modal opens
  useEffect(() => {
    if (isOpen) {
      console.log('🔄 ThreeStepWithdrawModal opened with props:', {
        initialCrypto,
        initialWithdrawalType,
        initialInterestAmount,
        isOpen
      });

      if (initialCrypto) {
        const cryptoSymbol = mapCryptoNameToSymbol(initialCrypto);
        console.log('💰 Setting crypto symbol:', { initialCrypto, cryptoSymbol });
        setSelectedCrypto(cryptoSymbol);
      }
      if (initialWithdrawalType) {
        setWithdrawalType(initialWithdrawalType);
      }
      // Reset form when opening
      setAmount('');
      setWalletAddress('');
      setMemo('');
      setActiveStep(0);
      setIsSubmitting(false);
      setUploadProgress(0);

      // CRITICAL: Force refresh wallet and investment balances when modal opens
      console.log('Withdrawal modal opened - refreshing all balance data');

      // Refresh wallet data
      if (fetchWallet) {
        fetchWallet().then(() => {
          console.log('Wallet data refreshed on modal open');
        }).catch(error => {
          console.error('Error refreshing wallet on modal open:', error);
        });
      }

      // Refresh withdrawable balance if crypto is already selected
      if (selectedCrypto && phase3ValidationEnabled) {
        console.log('🔄 ThreeStepWithdrawModal: Refreshing withdrawable balance on modal open for:', selectedCrypto);
        fetchWithdrawableBalance(selectedCrypto);
      }

      // Refresh investment balances
      const refreshInvestmentBalances = async () => {
        try {
          setLoadingBalances(true);
          const balances = await investmentBalanceService.getInvestmentBalances();
          setInvestmentBalances(balances);
          console.log('Investment balances refreshed on modal open:', balances);
        } catch (error) {
          console.error('Error refreshing investment balances on modal open:', error);
          setInvestmentBalances([]);
        } finally {
          setLoadingBalances(false);
        }
      };

      refreshInvestmentBalances();
    }
  }, [isOpen, initialCrypto, initialWithdrawalType, fetchWallet]);

  // Reset form when modal is closed
  useEffect(() => {
    if (!isOpen) {
      setAmount('');
      setWalletAddress('');
      setMemo('');
      setActiveStep(0);
      setIsSubmitting(false);
      setUploadProgress(0);
    }
  }, [isOpen]);

  // Handle network selection change
  const handleNetworkChange = (networkId: string) => {
    setSelectedNetwork(networkId);
  };

  // Get withdrawal fee based on selected network
  const getWithdrawalFee = () => {
    if (currentNetworkDetails) {
      return currentNetworkDetails.fee;
    }

    // Fallback to default fees if network details are not available
    switch(selectedCrypto) {
      case 'BTC': return 0.0005;
      case 'ETH': return 0.01;
      case 'USDT': return 2.00;
      case 'DOGE': return 1.00;
      case 'XRP': return 0.5;
      default: return 0.001;
    }
  };

  // Get minimum withdrawal amount based on selected cryptocurrency
  const getMinWithdrawal = () => {
    // Phase 3: Use minimum threshold from withdrawable balance if available
    if (phase3ValidationEnabled && withdrawableBalance) {
      return withdrawableBalance.minimumThreshold;
    }

    // Fallback to legacy minimums (updated for 20 USD equivalent)
    switch(selectedCrypto) {
      case 'BTC': return 0.0005; // ~20 USD at 40k price
      case 'ETH': return 0.008;  // ~20 USD at 2.5k price
      case 'USDT': return 20.00; // 20 USD
      case 'DOGE': return 200.00; // ~20 USD at 0.1 price
      case 'XRP': return 40; // ~20 USD at 0.5 price
      default: return 0.001;
    }
  };

  // Get precision for formatting based on selected cryptocurrency
  const getPrecision = () => {
    switch(selectedCrypto) {
      case 'BTC': return 8;
      case 'ETH': return 6;
      case 'USDT': return 2;
      case 'DOGE': return 2;
      case 'XRP': return 2;
      default: return 2;
    }
  };

  // Format amount with appropriate precision
  const formatAmount = (value: number) => {
    return value.toFixed(getPrecision());
  };

  // Get processing time based on selected network
  const getProcessingTime = () => {
    if (currentNetworkDetails) {
      return currentNetworkDetails.processingTime;
    }
    return '1 day'; // Default processing time
  };

  // Get balance for specific crypto and type with enhanced calculation
  const getBalanceForCryptoAndType = (crypto: string, type: 'interest' | 'commission') => {
    console.log(`Getting balance for ${crypto} (${type}):`, {
      investmentBalances,
      wallet: wallet?.assets,
      loading,
      loadingBalances
    });

    // For interest withdrawals, check investment package balances first
    if (type === 'interest' && investmentBalances.length > 0) {
      const investmentBalance = investmentBalances.find(b => b.currency === crypto);
      if (investmentBalance && investmentBalance.availableForWithdrawal > 0) {
        console.log(`Found investment balance for ${crypto}:`, investmentBalance.availableForWithdrawal);
        return investmentBalance.availableForWithdrawal;
      }
    }

    // Enhanced wallet balance calculation including approved deposits
    if (wallet && wallet.assets && wallet.assets.length > 0) {
      const asset = wallet.assets.find(a => a.symbol === crypto);
      if (asset) {
        let balance = 0;
        switch (type) {
          case 'interest':
            // Include both interest balance and main balance for approved deposits
            balance = (asset.interestBalance || 0) + (asset.balance || 0);
            break;
          case 'commission':
            balance = asset.commissionBalance || 0;
            break;
          default:
            balance = 0;
        }

        console.log(`Wallet balance for ${crypto} (${type}):`, {
          interestBalance: asset.interestBalance,
          mainBalance: asset.balance,
          commissionBalance: asset.commissionBalance,
          calculatedBalance: balance
        });

        return balance;
      }
    }

    // If loading, return 0
    if (loading || loadingBalances) {
      console.log(`Still loading balances for ${crypto}`);
      return 0;
    }

    console.log(`No balance found for ${crypto} (${type})`);
    return 0;
  };

  // Get available balance based on selected cryptocurrency and withdrawal type
  const getAvailableBalance = () => {
    // Phase 3: Use withdrawable balance if available
    if (phase3ValidationEnabled && withdrawableBalance) {
      if (withdrawalType === 'interest') {
        // During principal lock, only interest + commission are withdrawable
        if (withdrawableBalance.principalLocked) {
          return withdrawableBalance.interestAmount + withdrawableBalance.commissionAmount;
        }
        // After lock expires, total amount is withdrawable
        return withdrawableBalance.totalWithdrawable;
      } else if (withdrawalType === 'commission') {
        return withdrawableBalance.commissionAmount;
      }
    }

    // Fallback to legacy calculation
    return getBalanceForCryptoAndType(selectedCrypto, withdrawalType as 'interest' | 'commission');
  };

  // Next step in the wizard
  const nextStep = () => {
    if (activeStep === 0) {
      // Validate first step
      if (!amount || parseFloat(amount) <= 0) {
        toast({
          title: t('withdrawModal.invalidAmount', 'Invalid Amount'),
          description: t('withdrawModal.enterValidAmount', 'Please enter a valid amount.'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      const availableBalance = getAvailableBalance();
      if (parseFloat(amount) > availableBalance) {
        toast({
          title: t('withdrawModal.insufficientBalance', 'Insufficient Balance'),
          description: t('withdrawModal.insufficientBalanceDesc', 'The amount you want to withdraw exceeds your balance.'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      if (parseFloat(amount) < getMinWithdrawal()) {
        toast({
          title: t('withdrawModal.belowMinimum', 'Below Minimum Withdrawal Amount'),
          description: `Minimum withdrawal amount: ${formatAmount(getMinWithdrawal())} ${selectedCrypto}`,
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      // Validate network selection
      if (!selectedNetwork) {
        toast({
          title: t('withdrawModal.networkRequired', 'Network Required'),
          description: t('withdrawModal.selectNetwork', 'Please select a network for your withdrawal.'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }
    } else if (activeStep === 1) {
      // Validate second step
      if (!walletAddress) {
        toast({
          title: t('withdrawModal.addressRequired', 'Wallet Address Required'),
          description: t('withdrawModal.enterAddress', 'Please enter a valid wallet address.'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }
    }

    setActiveStep(activeStep + 1);
  };

  // Previous step in the wizard
  const prevStep = () => {
    setActiveStep(activeStep - 1);
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Validate form
    if (!amount || parseFloat(amount) <= 0) {
      toast({
        title: t('withdrawModal.invalidAmount', 'Invalid Amount'),
        description: t('withdrawModal.enterValidAmount', 'Please enter a valid amount.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const availableBalance = getAvailableBalance();
    if (parseFloat(amount) > availableBalance) {
      toast({
        title: t('withdrawModal.insufficientBalance', 'Insufficient Balance'),
        description: t('withdrawModal.insufficientBalanceDesc', 'The amount you want to withdraw exceeds your balance.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (parseFloat(amount) < getMinWithdrawal()) {
      toast({
        title: t('withdrawModal.belowMinimum', 'Below Minimum Withdrawal Amount'),
        description: `Minimum withdrawal amount: ${formatAmount(getMinWithdrawal())} ${selectedCrypto}`,
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // Validate network selection
    if (!selectedNetwork) {
      toast({
        title: t('withdrawModal.networkRequired', 'Network Required'),
        description: t('withdrawModal.selectNetwork', 'Please select a network for your withdrawal.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (!walletAddress) {
      toast({
        title: t('withdrawModal.addressRequired', 'Wallet Address Required'),
        description: t('withdrawModal.enterAddress', 'Please enter a valid wallet address.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // Start submission process
    setIsSubmitting(true);
    setUploadProgress(0);

    try {
      // Simulate progress for better UX
      const interval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(interval);
            return 90;
          }
          return prev + 10;
        });
      }, 300);

      // Import wallet service dynamically to avoid circular dependencies
      const { walletService } = await import('../../services/walletService');

      // Send withdrawal request to backend
      await walletService.withdrawAsset({
        asset: selectedCrypto,
        amount: parseFloat(amount),
        address: walletAddress,
        memo: memo || undefined,
        withdrawalType: withdrawalType as 'interest' | 'commission',
        network: selectedNetwork,
        blockchainNetwork: selectedNetwork // Add blockchainNetwork parameter for backend compatibility
      });

      // Complete progress
      clearInterval(interval);
      setUploadProgress(100);

      toast({
        title: t('withdrawModal.successTitle', 'Withdrawal Successful'),
        description: t('withdrawModal.successDescription', 'Your withdrawal request has been received. Your transaction will be processed as soon as possible.'),
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      // Refresh wallet data to show updated balances
      if (fetchWallet) {
        await fetchWallet();
      }

      // Refresh withdrawable balance to show updated amounts
      if (selectedCrypto && phase3ValidationEnabled) {
        console.log('🔄 ThreeStepWithdrawModal: Refreshing withdrawable balance after successful withdrawal');
        await fetchWithdrawableBalance(selectedCrypto);
      }

      // Reset form and close modal
      setAmount('');
      setWalletAddress('');
      setMemo('');
      setActiveStep(0);
      onClose();
    } catch (error: unknown) {
      console.error('Withdrawal error:', error);

      let errorMessage = t('withdrawModal.errorDescription', 'An error occurred while processing your withdrawal.');
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null && 'response' in error &&
                error.response && typeof error.response === 'object' &&
                'data' in error.response && error.response.data &&
                typeof error.response.data === 'object' && 'message' in error.response.data) {
        errorMessage = (error.response.data as { message: string }).message;
      }

      toast({
        title: t('withdrawModal.errorTitle', 'Withdrawal Failed'),
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md" isCentered>
      <ModalOverlay backdropFilter="blur(5px)" />
      <ModalContent
        bg="#0B0E11"
        borderColor="#2B3139"
        borderWidth="1px"
        mx={{ base: 2, md: "auto" }}
        maxH={{ base: "calc(100vh - 40px)", md: "calc(100vh - 80px)" }}
        overflowY="auto"
        height="auto"
      >
        <ModalHeader color="#F0B90B" borderBottomWidth="1px" borderColor="#2B3139">
          {t('withdrawModal.title', 'Withdrawal Transaction')}
        </ModalHeader>
        <ModalCloseButton color="#EAECEF" />

        <ModalBody py={6}>
          <Tabs index={activeStep} onChange={setActiveStep} variant="unstyled" colorScheme="yellow">
            <TabList mb={4}>
              <Tab
                _selected={{ color: "#F0B90B", fontWeight: "bold" }}
                color={activeStep === 0 ? "#F0B90B" : "#848E9C"}
                fontWeight={activeStep === 0 ? "bold" : "normal"}
              >
                1. Amount
              </Tab>
              <Tab
                _selected={{ color: "#F0B90B", fontWeight: "bold" }}
                color={activeStep === 1 ? "#F0B90B" : "#848E9C"}
                fontWeight={activeStep === 1 ? "bold" : "normal"}
                isDisabled={activeStep < 1}
              >
                2. Address
              </Tab>
              <Tab
                _selected={{ color: "#F0B90B", fontWeight: "bold" }}
                color={activeStep === 2 ? "#F0B90B" : "#848E9C"}
                fontWeight={activeStep === 2 ? "bold" : "normal"}
                isDisabled={activeStep < 2}
              >
                3. Confirm
              </Tab>
            </TabList>

            <TabPanels>
              {/* Step 1: Amount Details */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <Box p={4} bg="#1E2329" borderRadius="md">
                    <Text color="#F0B90B" fontSize="md" fontWeight="bold" mb={3}>Select Withdrawal Type</Text>

                    {/* Phase 3: Principal Lock Status Display */}
                    {phase3ValidationEnabled && withdrawableBalance && withdrawableBalance.principalLocked && (
                      <Alert status="warning" borderRadius="md" mb={4} bg="#F0B90B22">
                        <AlertIcon color="#F0B90B" />
                        <Box>
                          <AlertTitle color="#F0B90B">Principal Lock Active</AlertTitle>
                          <AlertDescription color="#EAECEF" fontSize="sm">
                            Your principal amount ({withdrawableBalance.principalAmount.toFixed(2)} {selectedCrypto}) is locked for {withdrawableBalance.daysUntilUnlock} more days.
                            Only interest earnings and commission are withdrawable during this period.
                          </AlertDescription>
                          {withdrawableBalance.principalLockUntil && (
                            <Text fontSize="xs" color="#848E9C" mt={1}>
                              Principal unlocks on: {new Date(withdrawableBalance.principalLockUntil).toLocaleDateString()}
                            </Text>
                          )}
                        </Box>
                      </Alert>
                    )}

                    {/* Phase 3: Validation Error Display */}
                    {withdrawalValidationError && (
                      <Alert status="error" borderRadius="md" mb={4} bg="#F8496022">
                        <AlertIcon color="#F84960" />
                        <Box>
                          <AlertTitle color="#F84960">Withdrawal Validation Error</AlertTitle>
                          <AlertDescription color="#EAECEF" fontSize="sm">
                            {withdrawalValidationError}
                          </AlertDescription>
                        </Box>
                      </Alert>
                    )}

                    <RadioGroup onChange={(value) => setWithdrawalType(value as 'interest' | 'commission')} value={withdrawalType} mb={4}>
                      <Stack direction="column" spacing={3}>

                        <Radio
                          value="interest"
                          colorScheme="yellow"
                          borderColor="#2B3139"
                        >
                          <VStack align="stretch" w="100%" spacing={2}>
                            <HStack justify="space-between" w="100%">
                              <HStack spacing={3}>
                                <Text color="#EAECEF" minW="140px">Interest Earnings</Text>
                                <Badge colorScheme="green">Available</Badge>
                              </HStack>
                              <Text color="#0ECB81" fontWeight="bold">
                                {loadingWithdrawableBalance || loadingBalances ? '...' : (() => {
                                  // CRITICAL FIX: Use initialInterestAmount if provided from investment cards
                                  const useInitialAmount = initialInterestAmount && withdrawalType === 'interest' && initialInterestAmount > 0;
                                  const fallbackAmount = phase3ValidationEnabled && withdrawableBalance
                                    ? (withdrawableBalance.principalLocked
                                        ? withdrawableBalance.interestAmount + withdrawableBalance.commissionAmount
                                        : withdrawableBalance.totalWithdrawable)
                                    : getBalanceForCryptoAndType(selectedCrypto, 'interest');

                                  const displayAmount = useInitialAmount ? initialInterestAmount : fallbackAmount;

                                  console.log('💰 Interest earnings display logic:', {
                                    initialInterestAmount,
                                    withdrawalType,
                                    selectedCrypto,
                                    useInitialAmount,
                                    fallbackAmount,
                                    displayAmount
                                  });

                                  return formatAmount(displayAmount);
                                })()} {selectedCrypto}
                              </Text>
                            </HStack>
                            {withdrawalType === 'interest' && (
                              <Box bg="#0ECB8122" p={2} borderRadius="md" fontSize="xs">
                                {/* Phase 3: Enhanced balance breakdown */}
                                {phase3ValidationEnabled && withdrawableBalance ? (
                                  <>
                                    <Text color="#0ECB81" fontWeight="bold" mb={1}>
                                      💰 Phase 3: Detailed Balance Breakdown
                                    </Text>
                                    <VStack spacing={1} align="stretch">
                                      <Flex justify="space-between">
                                        <Text color="#EAECEF" fontSize="xs">Principal Amount:</Text>
                                        <Text color={withdrawableBalance.principalLocked ? "#F0B90B" : "#0ECB81"} fontSize="xs">
                                          {formatAmount(withdrawableBalance.principalAmount)} {selectedCrypto}
                                          {withdrawableBalance.principalLocked && " (LOCKED)"}
                                        </Text>
                                      </Flex>
                                      <Flex justify="space-between">
                                        <Text color="#EAECEF" fontSize="xs">Interest Earnings:</Text>
                                        <Text color="#0ECB81" fontSize="xs">
                                          {formatAmount(withdrawableBalance.interestAmount)} {selectedCrypto}
                                        </Text>
                                      </Flex>
                                      <Flex justify="space-between">
                                        <Text color="#EAECEF" fontSize="xs">Commission:</Text>
                                        <Text color="#0ECB81" fontSize="xs">
                                          {formatAmount(withdrawableBalance.commissionAmount)} {selectedCrypto}
                                        </Text>
                                      </Flex>
                                      <Divider borderColor="#2B3139" my={1} />
                                      <Flex justify="space-between">
                                        <Text color="#0ECB81" fontSize="xs" fontWeight="bold">Total Withdrawable:</Text>
                                        <Text color="#0ECB81" fontSize="xs" fontWeight="bold">
                                          {formatAmount(withdrawableBalance.principalLocked
                                            ? withdrawableBalance.interestAmount + withdrawableBalance.commissionAmount
                                            : withdrawableBalance.totalWithdrawable)} {selectedCrypto}
                                        </Text>
                                      </Flex>
                                      {withdrawableBalance.principalLocked && (
                                        <Text color="#F0B90B" fontSize="xs" fontStyle="italic">
                                          Principal unlocks in {withdrawableBalance.daysUntilUnlock} days
                                        </Text>
                                      )}
                                    </VStack>
                                  </>
                                ) : (
                                  <>
                                    <Text color="#0ECB81" fontWeight="bold" mb={1}>
                                      📊 Investment Package Earnings:
                                    </Text>
                                    {investmentBalances
                                      .filter(balance => balance.currency === selectedCrypto && balance.availableForWithdrawal > 0)
                                      .map((balance, index) => (
                                        <Text key={index} color="#EAECEF" fontSize="xs">
                                          • {balance.currency}: {formatAmount(balance.availableForWithdrawal)} available
                                          {balance.activePackages > 0 && ` (${balance.activePackages} active packages)`}
                                        </Text>
                                      ))
                                    }
                                    {investmentBalances.filter(balance => balance.currency === selectedCrypto && balance.availableForWithdrawal > 0).length === 0 && (
                                      <Text color="#848E9C" fontSize="xs">
                                        No investment earnings available for {selectedCrypto}
                                      </Text>
                                    )}
                                  </>
                                )}
                              </Box>
                            )}
                          </VStack>
                        </Radio>

                        <Radio
                          value="commission"
                          colorScheme="yellow"
                          borderColor="#2B3139"
                        >
                          <HStack justify="space-between" w="100%">
                            <HStack spacing={3}>
                              <Text color="#EAECEF" minW="140px">Referral Commission</Text>
                              <Badge colorScheme="green">Available</Badge>
                            </HStack>
                            <Text color="#0ECB81" fontWeight="bold">
                              {loadingWithdrawableBalance || loading ? '...' : formatAmount(
                                phase3ValidationEnabled && withdrawableBalance
                                  ? withdrawableBalance.commissionAmount
                                  : getBalanceForCryptoAndType(selectedCrypto, 'commission')
                              )} {selectedCrypto}
                            </Text>
                          </HStack>
                        </Radio>
                      </Stack>
                    </RadioGroup>

                    <Divider borderColor="#2B3139" my={3} />

                    <FormControl isRequired mt={3}>
                      <FormLabel color="#848E9C" fontSize="sm">Cryptocurrency</FormLabel>
                      <Select
                        value={selectedCrypto}
                        onChange={(e) => setSelectedCrypto(e.target.value)}
                        bg="#0B0E11"
                        borderColor="#2B3139"
                        color="#EAECEF"
                        _hover={{ borderColor: "#F0B90B" }}
                      >
                        <option value="BTC">Bitcoin (BTC)</option>
                        <option value="ETH">Ethereum (ETH)</option>
                        <option value="USDT">Tether (USDT)</option>
                        <option value="TRX">Tron (TRX)</option>
                        <option value="BNB">Binance Coin (BNB)</option>
                        <option value="DOGE">Dogecoin (DOGE)</option>
                        <option value="SOL">Solana (SOL)</option>
                      </Select>
                    </FormControl>

                    {/* Network Selection */}
                    <NetworkSelector
                      networks={networkOptions}
                      selectedNetwork={selectedNetwork}
                      onChange={handleNetworkChange}
                      isRequired={true}
                      label={t('withdrawModal.network', 'Select Network')}
                      helperText={t('withdrawModal.networkHelperText', `Choose the network for your ${selectedCrypto} withdrawal`)}
                      currency={selectedCrypto}
                    />

                    <FormControl isRequired mt={4}>
                      <FormLabel color="#848E9C" fontSize="sm">
                        Withdrawal Amount
                        <Text as="span" color="#0ECB81" fontSize="xs" ml={2}>
                          (Available: {loading ? '...' : formatAmount(getAvailableBalance())} {selectedCrypto})
                        </Text>
                      </FormLabel>
                      <NumberInput
                        value={amount}
                        onChange={setAmount}
                        max={getAvailableBalance()}
                        min={getMinWithdrawal()}
                        precision={getPrecision()}
                        w="100%"
                        isDisabled={getAvailableBalance() <= 0}
                      >
                        <NumberInputField
                          placeholder={getAvailableBalance() <= 0 ? "No balance available" : "0.00"}
                          bg="#0B0E11"
                          borderColor="#2B3139"
                          color="#EAECEF"
                          _hover={{ borderColor: "#F0B90B" }}
                        />
                        <NumberInputStepper>
                          <NumberIncrementStepper color="#EAECEF" />
                          <NumberDecrementStepper color="#EAECEF" />
                        </NumberInputStepper>
                      </NumberInput>
                      {getAvailableBalance() <= 0 && !loading && (
                        <Text color="#F6465D" fontSize="xs" mt={1}>
                          No {withdrawalType === 'interest' ? 'interest earnings' : 'referral commission'} available for {selectedCrypto}
                        </Text>
                      )}
                    </FormControl>

                    <HStack spacing={2} mt={3}>
                      <Button
                        size="sm"
                        onClick={() => setAmount(formatAmount(getAvailableBalance() * 0.25))}
                        variant="outline"
                        borderColor="#2B3139"
                        color="#848E9C"
                        _hover={{ borderColor: "#F0B90B" }}
                        isDisabled={getAvailableBalance() <= 0}
                      >
                        25%
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => setAmount(formatAmount(getAvailableBalance() * 0.5))}
                        variant="outline"
                        borderColor="#2B3139"
                        color="#848E9C"
                        _hover={{ borderColor: "#F0B90B" }}
                        isDisabled={getAvailableBalance() <= 0}
                      >
                        50%
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => setAmount(formatAmount(getAvailableBalance() * 0.75))}
                        variant="outline"
                        borderColor="#2B3139"
                        color="#848E9C"
                        _hover={{ borderColor: "#F0B90B" }}
                        isDisabled={getAvailableBalance() <= 0}
                      >
                        75%
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => setAmount(formatAmount(getAvailableBalance()))}
                        variant="outline"
                        borderColor="#2B3139"
                        color="#848E9C"
                        _hover={{ borderColor: "#F0B90B" }}
                        isDisabled={getAvailableBalance() <= 0}
                      >
                        100%
                      </Button>
                    </HStack>
                  </Box>

                  <Box p={4} bg="#1E2329" borderRadius="md">
                    <Flex align="center" mb={2}>
                      <Icon as={FaInfoCircle} color="#F0B90B" mr={2} />
                      <Text color="#F0B90B" fontWeight="bold">Withdrawal Information</Text>
                    </Flex>

                    <Divider borderColor="#2B3139" mb={3} />

                    <VStack spacing={2} align="stretch">
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">Minimum Withdrawal:</Text>
                        <Text color="#EAECEF" fontSize="sm">
                          {formatAmount(getMinWithdrawal())} {selectedCrypto}
                          {phase3ValidationEnabled && withdrawableBalance && (
                            <Text as="span" color="#F0B90B" fontSize="xs" ml={1}>
                              (Phase 3)
                            </Text>
                          )}
                        </Text>
                      </Flex>
                      {/* Phase 3: Additional withdrawal info */}
                      {phase3ValidationEnabled && withdrawableBalance && (
                        <>
                          <Flex justify="space-between">
                            <Text color="#848E9C" fontSize="sm">Available Balance:</Text>
                            <Text color="#0ECB81" fontSize="sm">
                              {formatAmount(withdrawableBalance.principalLocked
                                ? withdrawableBalance.interestAmount + withdrawableBalance.commissionAmount
                                : withdrawableBalance.totalWithdrawable)} {selectedCrypto}
                            </Text>
                          </Flex>
                          {withdrawableBalance.principalLocked && (
                            <Flex justify="space-between">
                              <Text color="#848E9C" fontSize="sm">Principal Status:</Text>
                              <Text color="#F0B90B" fontSize="sm">
                                Locked ({withdrawableBalance.daysUntilUnlock} days)
                              </Text>
                            </Flex>
                          )}
                        </>
                      )}
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">Withdrawal Fee:</Text>
                        <Text color="#EAECEF" fontSize="sm">
                          {formatAmount(getWithdrawalFee())} {selectedCrypto}
                        </Text>
                      </Flex>
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">Processing Time:</Text>
                        <Text color="#EAECEF" fontSize="sm">{getProcessingTime()}</Text>
                      </Flex>
                    </VStack>
                  </Box>
                </VStack>

                <Flex justify="flex-end" mt={6}>
                  <Button
                    bg="#F0B90B"
                    color="#0B0E11"
                    _hover={{ bg: "#F8D12F" }}
                    onClick={nextStep}
                    isDisabled={
                      !amount ||
                      parseFloat(amount) <= 0 ||
                      parseFloat(amount) > getAvailableBalance() ||
                      parseFloat(amount) < getMinWithdrawal() ||
                      !selectedNetwork ||
                      getAvailableBalance() <= 0
                    }
                    w="100%"
                  >
                    Next
                  </Button>
                </Flex>
              </TabPanel>

              {/* Step 2: Wallet Address */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <Box p={4} bg="#1E2329" borderRadius="md">
                    <Text color="#F0B90B" fontSize="md" fontWeight="bold" mb={3}>Wallet Information</Text>

                    {currentNetworkDetails && (
                      <Alert status="info" borderRadius="md" mb={4} bg="#0ECB8122">
                        <AlertIcon color="#0ECB81" />
                        <Box>
                          <Text fontWeight="bold" color="#EAECEF">
                            {t('withdrawModal.selectedNetwork', 'Selected Network')}
                          </Text>
                          <Text fontSize="sm" color="#EAECEF">
                            {currentNetworkDetails.name} - {currentNetworkDetails.description}
                          </Text>
                          <Text fontSize="xs" color="#EAECEF" mt={1}>
                            {t('withdrawModal.processingTime', 'Processing Time')}: {currentNetworkDetails.processingTime}
                          </Text>
                          <Text fontSize="xs" color="#EAECEF">
                            {t('withdrawModal.networkFee', 'Network Fee')}: {currentNetworkDetails.fee} {selectedCrypto}
                          </Text>
                        </Box>
                      </Alert>
                    )}

                    {currentNetworkDetails?.warningMessage && (
                      <Alert status="warning" borderRadius="md" mb={4} bg="#F0B90B22">
                        <AlertIcon color="#F0B90B" />
                        <Box>
                          <Text fontWeight="bold">{t('withdrawModal.networkWarning', 'Important Network Information')}</Text>
                          <Text fontSize="sm">{currentNetworkDetails.warningMessage}</Text>
                        </Box>
                      </Alert>
                    )}

                    <FormControl isRequired>
                      <FormLabel color="#848E9C" fontSize="sm">
                        {selectedCrypto} Wallet Address
                      </FormLabel>
                      <Input
                        value={walletAddress}
                        onChange={(e) => setWalletAddress(e.target.value)}
                        placeholder="Enter your wallet address"
                        bg="#0B0E11"
                        borderColor="#2B3139"
                        color="#EAECEF"
                        _hover={{ borderColor: "#F0B90B" }}
                        fontFamily="monospace"
                      />
                      <Text color="#848E9C" fontSize="xs" mt={1}>
                        Address accuracy is important. Funds sent to incorrect addresses cannot be recovered.
                      </Text>
                    </FormControl>

                    {(selectedCrypto === 'XRP' || selectedCrypto === 'DOGE') && (
                      <FormControl mt={4}>
                        <FormLabel color="#848E9C" fontSize="sm">
                          Memo / Tag (If Required)
                        </FormLabel>
                        <Input
                          value={memo}
                          onChange={(e) => setMemo(e.target.value)}
                          placeholder="Enter Memo or Tag information"
                          bg="#0B0E11"
                          borderColor="#2B3139"
                          color="#EAECEF"
                          _hover={{ borderColor: "#F0B90B" }}
                        />
                        <Text color="#848E9C" fontSize="xs" mt={1}>
                          * Required for some cryptocurrencies (e.g. XRP)
                        </Text>
                      </FormControl>
                    )}
                  </Box>

                  <Alert status="info" borderRadius="md" bg="#0ECB8122">
                    <AlertIcon color="#0ECB81" />
                    <Box>
                      <AlertTitle color="#0ECB81">Important Information</AlertTitle>
                      <AlertDescription color="#EAECEF" fontSize="sm">
                        Your withdrawal should be processed within 1 day. Please ensure you have entered the correct wallet address.
                      </AlertDescription>
                    </Box>
                  </Alert>
                </VStack>

                <Flex justify="space-between" mt={6}>
                  <Button
                    variant="outline"
                    onClick={prevStep}
                    borderColor="#2B3139"
                    color="#EAECEF"
                    _hover={{ borderColor: "#F0B90B" }}
                    flex="1"
                    mr={2}
                  >
                    Back
                  </Button>
                  <Button
                    bg="#F0B90B"
                    color="#0B0E11"
                    _hover={{ bg: "#F8D12F" }}
                    onClick={nextStep}
                    isDisabled={!walletAddress}
                    flex="1"
                    ml={2}
                  >
                    Next
                  </Button>
                </Flex>
              </TabPanel>

              {/* Step 3: Confirmation */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <Box p={4} bg="#1E2329" borderRadius="md">
                    <Text color="#F0B90B" fontSize="md" fontWeight="bold" mb={3}>Transaction Confirmation</Text>

                    <VStack spacing={3} align="stretch">
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">Withdrawal Type:</Text>
                        <Text color="#EAECEF" fontWeight="bold" fontSize="sm">
                          {withdrawalType === 'interest' ? 'Interest Earnings' : 'Referral Commission'}
                        </Text>
                      </Flex>
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">Cryptocurrency:</Text>
                        <Text color="#EAECEF" fontWeight="bold" fontSize="sm">{selectedCrypto}</Text>
                      </Flex>
                      {currentNetworkDetails && (
                        <Flex justify="space-between">
                          <Text color="#848E9C" fontSize="sm">Network:</Text>
                          <Text color="#EAECEF" fontWeight="bold" fontSize="sm">{currentNetworkDetails.name}</Text>
                        </Flex>
                      )}
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">Amount:</Text>
                        <Text color="#EAECEF" fontWeight="bold" fontSize="sm">
                          {formatAmount(parseFloat(amount))} {selectedCrypto}
                        </Text>
                      </Flex>
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">Withdrawal Fee:</Text>
                        <Text color="#EAECEF" fontWeight="bold" fontSize="sm">
                          {formatAmount(getWithdrawalFee())} {selectedCrypto}
                        </Text>
                      </Flex>
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">Net Amount:</Text>
                        <Text color="#0ECB81" fontWeight="bold" fontSize="sm">
                          {formatAmount(parseFloat(amount) - getWithdrawalFee())} {selectedCrypto}
                        </Text>
                      </Flex>
                      <Divider borderColor="#2B3139" my={1} />
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">Wallet Address:</Text>
                        <Text color="#EAECEF" fontWeight="bold" fontFamily="monospace" fontSize="xs" maxW="180px" isTruncated>
                          {walletAddress}
                        </Text>
                      </Flex>
                      {memo && (
                        <Flex justify="space-between">
                          <Text color="#848E9C" fontSize="sm">Memo / Tag:</Text>
                          <Text color="#EAECEF" fontWeight="bold" fontSize="sm">{memo}</Text>
                        </Flex>
                      )}
                      <Divider borderColor="#2B3139" my={1} />
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">Processing Time:</Text>
                        <Text color="#EAECEF" fontWeight="bold" fontSize="sm">{getProcessingTime()}</Text>
                      </Flex>
                    </VStack>
                  </Box>

                  <Alert status="warning" borderRadius="md" bg="#F0B90B22">
                    <AlertIcon color="#F0B90B" />
                    <Box>
                      <AlertDescription color="#EAECEF" fontSize="sm">
                        Withdrawal cannot be cancelled after confirmation. Please ensure all information is correct.
                        {currentNetworkDetails && (
                          <Text fontSize="xs" fontWeight="bold" color="#F0B90B" mt={1}>
                            {t('withdrawModal.networkFinalWarning', `IMPORTANT: This withdrawal will be processed on the ${currentNetworkDetails.name}. Make sure your receiving wallet supports this network.`)}
                          </Text>
                        )}
                      </AlertDescription>
                    </Box>
                  </Alert>
                </VStack>

                <Flex justify="space-between" mt={6}>
                  <Button
                    variant="outline"
                    onClick={prevStep}
                    borderColor="#2B3139"
                    color="#EAECEF"
                    _hover={{ borderColor: "#F0B90B" }}
                    flex="1"
                    mr={2}
                  >
                    Back
                  </Button>
                  <Button
                    bg="#F0B90B"
                    color="#0B0E11"
                    _hover={{ bg: "#F8D12F" }}
                    onClick={handleSubmit}
                    isLoading={isSubmitting}
                    loadingText="Processing..."
                    flex="1"
                    ml={2}
                  >
                    Confirm
                  </Button>
                </Flex>

                {isSubmitting && (
                  <Box mt={4}>
                    <Text fontSize="xs" color="#848E9C" mb={2}>
                      Processing your transaction...
                    </Text>
                    <Progress value={uploadProgress} size="xs" colorScheme="yellow" borderRadius="full" />
                  </Box>
                )}
              </TabPanel>
            </TabPanels>
          </Tabs>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default ThreeStepWithdrawModal;