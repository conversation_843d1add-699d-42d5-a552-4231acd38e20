import { apiClient } from '../utils/apiClient';

/**
 * Enhanced Wallet Service for Real-Time Balance Management
 */

export interface WalletBalance {
  cryptocurrency: string;
  balance: number;
  interestBalance: number;
  commissionBalance: number;
  usdValue: number;
  lastUpdated: string;
}

export interface WalletStats {
  totalUSDValue: number;
  totalInterestUSD: number;
  totalCommissionUSD: number;
  totalEarnings: number;
  activeCryptocurrencies: number;
  lastUpdated: string;
}

export interface EnhancedWalletData {
  userId: string;
  assets: Array<{
    symbol: string;
    balance: number;
    commissionBalance: number;
    interestBalance: number;
    mode: 'commission' | 'interest';
    network: string;
    // Enhanced fields
    principalAmount: number;
    totalEarned: number;
    dailyInterestRate: number;
    lastInterestDate?: string;
    isLocked: boolean;
    lockExpiryDate?: string;
    daysUntilUnlock: number;
    totalInvestments: number;
    activePackages: number;
  }>;
  realTimeData: {
    [cryptocurrency: string]: {
      currentInterest: number;
      nextDistribution: string;
      dailyRate: number;
      isActive: boolean;
    };
  };
  totalCommissionEarned: number;
  totalInterestEarned: number;
  lastUpdated: string;
}

class EnhancedWalletService {
  private cache: Map<string, any> = new Map();
  private cacheTimeout = 30000; // 30 seconds

  /**
   * Get enhanced wallet data
   */
  async getEnhancedWalletData(): Promise<{ data: { status: string; data: { wallet: EnhancedWalletData; metadata?: any } } }> {
    try {
      console.log('🔄 EnhancedWalletService: Fetching enhanced wallet data');

      // Use wallet endpoint
      const walletResponse = await apiClient.get('/wallets/info');
      const enhancedData = this.transformLegacyToEnhanced(walletResponse);

      console.log('✅ EnhancedWalletService: Wallet data transformed to enhanced format');

      return {
        data: {
          status: 'success',
          data: {
            wallet: enhancedData,
            metadata: {
              source: 'wallet_system',
              lastUpdated: new Date().toISOString()
            }
          }
        }
      };

    } catch (error) {
      console.error('❌ EnhancedWalletService: Error fetching wallet data:', error);
      
      // Return mock data for development
      const mockData = this.getMockEnhancedData();
      return {
        data: {
          status: 'success',
          data: {
            wallet: mockData,
            metadata: {
              source: 'mock_data',
              lastUpdated: new Date().toISOString()
            }
          }
        }
      };
    }
  }

  /**
   * Force refresh wallet data
   */
  async forceRefreshWalletData(): Promise<{ data: { status: string; data: { wallet: EnhancedWalletData } } }> {
    try {
      console.log('🔄 EnhancedWalletService: Force refreshing wallet data');

      // Clear cache
      this.cache.clear();

      // Force sync on backend
      try {
        await apiClient.post('/wallets/sync');
        console.log('✅ EnhancedWalletService: Backend wallet sync triggered');
      } catch (syncError) {
        console.warn('⚠️ EnhancedWalletService: Backend sync failed:', syncError);
      }

      // Get fresh data
      return await this.getEnhancedWalletData();
    } catch (error) {
      console.error('❌ EnhancedWalletService: Error force refreshing:', error);
      throw error;
    }
  }

  /**
   * Transform wallet response to enhanced wallet format
   */
  private transformUnifiedToEnhanced(walletResponse: any): EnhancedWalletData {
    const { balances, stats } = walletResponse;
    
    const assets = Object.keys(balances).map(crypto => ({
      symbol: crypto,
      balance: balances[crypto].balance || 0,
      commissionBalance: balances[crypto].commissionBalance || 0,
      interestBalance: balances[crypto].interestBalance || 0,
      mode: 'commission' as const,
      network: this.getNetworkForCrypto(crypto),
      // Enhanced fields
      principalAmount: balances[crypto].balance || 0,
      totalEarned: (balances[crypto].commissionBalance || 0) + (balances[crypto].interestBalance || 0),
      dailyInterestRate: 0.01, // 1% daily
      lastInterestDate: new Date().toISOString(),
      isLocked: false,
      lockExpiryDate: null,
      daysUntilUnlock: 0,
      totalInvestments: balances[crypto].balance || 0,
      activePackages: balances[crypto].balance > 0 ? 1 : 0
    }));

    const realTimeData: any = {};
    Object.keys(balances).forEach(crypto => {
      realTimeData[crypto] = {
        currentInterest: (balances[crypto].balance || 0) * 0.01, // 1% daily
        nextDistribution: this.getNextDistributionTime(),
        dailyRate: 0.01,
        isActive: (balances[crypto].balance || 0) > 0
      };
    });

    return {
      userId: 'current_user',
      assets,
      realTimeData,
      totalCommissionEarned: stats?.totalCommissionUSD || 0,
      totalInterestEarned: stats?.totalInterestUSD || 0,
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Transform legacy wallet response to enhanced format
   */
  private transformLegacyToEnhanced(legacyResponse: any): EnhancedWalletData {
    const assets = (legacyResponse.assets || []).map((asset: any) => ({
      symbol: asset.symbol,
      balance: asset.balance || 0,
      commissionBalance: asset.commissionBalance || 0,
      interestBalance: asset.interestBalance || 0,
      mode: asset.mode || 'commission',
      network: asset.network || this.getNetworkForCrypto(asset.symbol),
      // Enhanced fields
      principalAmount: asset.balance || 0,
      totalEarned: (asset.commissionBalance || 0) + (asset.interestBalance || 0),
      dailyInterestRate: 0.01,
      lastInterestDate: asset.updatedAt || new Date().toISOString(),
      isLocked: false,
      lockExpiryDate: null,
      daysUntilUnlock: 0,
      totalInvestments: asset.balance || 0,
      activePackages: asset.balance > 0 ? 1 : 0
    }));

    const realTimeData: any = {};
    assets.forEach((asset: any) => {
      realTimeData[asset.symbol] = {
        currentInterest: asset.balance * 0.01,
        nextDistribution: this.getNextDistributionTime(),
        dailyRate: 0.01,
        isActive: asset.balance > 0
      };
    });

    return {
      userId: legacyResponse.userId || 'current_user',
      assets,
      realTimeData,
      totalCommissionEarned: legacyResponse.totalCommissionEarned || 0,
      totalInterestEarned: legacyResponse.totalInterestEarned || 0,
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Get mock enhanced data for development
   */
  private getMockEnhancedData(): EnhancedWalletData {
    const mockAssets = [
      {
        symbol: 'TRX',
        balance: 50000,
        commissionBalance: 2500,
        interestBalance: 1500,
        mode: 'commission' as const,
        network: 'Tron',
        principalAmount: 50000,
        totalEarned: 4000,
        dailyInterestRate: 0.01,
        lastInterestDate: new Date().toISOString(),
        isLocked: false,
        lockExpiryDate: null,
        daysUntilUnlock: 0,
        totalInvestments: 50000,
        activePackages: 1
      },
      {
        symbol: 'USDT',
        balance: 1000,
        commissionBalance: 50,
        interestBalance: 30,
        mode: 'commission' as const,
        network: 'Tron',
        principalAmount: 1000,
        totalEarned: 80,
        dailyInterestRate: 0.01,
        lastInterestDate: new Date().toISOString(),
        isLocked: false,
        lockExpiryDate: null,
        daysUntilUnlock: 0,
        totalInvestments: 1000,
        activePackages: 1
      }
    ];

    const realTimeData: any = {};
    mockAssets.forEach(asset => {
      realTimeData[asset.symbol] = {
        currentInterest: asset.balance * 0.01,
        nextDistribution: this.getNextDistributionTime(),
        dailyRate: 0.01,
        isActive: true
      };
    });

    return {
      userId: 'mock_user',
      assets: mockAssets,
      realTimeData,
      totalCommissionEarned: 2580,
      totalInterestEarned: 1530,
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Get network for cryptocurrency
   */
  private getNetworkForCrypto(crypto: string): string {
    const networks: { [key: string]: string } = {
      'BTC': 'Bitcoin',
      'ETH': 'Ethereum',
      'USDT': 'Tron',
      'TRX': 'Tron',
      'BNB': 'BSC',
      'SOL': 'Solana',
      'DOGE': 'Dogecoin'
    };
    return networks[crypto.toUpperCase()] || 'Unknown';
  }

  /**
   * Get next distribution time (next 03:00 UTC)
   */
  private getNextDistributionTime(): string {
    const now = new Date();
    const next = new Date(now);
    next.setUTCHours(3, 0, 0, 0);

    if (next <= now) {
      next.setUTCDate(next.getUTCDate() + 1);
    }

    return next.toISOString();
  }

  /**
   * Get withdrawable balance for specific cryptocurrency
   */
  async getWithdrawableBalance(cryptocurrency: string, type: 'balance' | 'interest' | 'commission' = 'balance'): Promise<any> {
    console.log(`🔍 EnhancedWalletService: Calling API for ${cryptocurrency} with type ${type}`);

    // Use VITE_API_URL directly for withdrawable balance endpoint
    const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001/api';
    const fullUrl = `${API_URL}/wallets/withdrawable-balance/${cryptocurrency}?type=${type}`;

    console.log(`🔄 EnhancedWalletService: Calling direct fetch to: ${fullUrl}`);

    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies for authentication
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log(`✅ EnhancedWalletService: API response:`, data);
    return data;
  }

  /**
   * Sync wallet data
   */
  async syncWalletData(): Promise<boolean> {
    try {
      await apiClient.post('/wallets/sync');
      console.log('✅ EnhancedWalletService: Wallet data synced');
      return true;
    } catch (error) {
      console.error('❌ EnhancedWalletService: Error syncing wallet data:', error);
      return false;
    }
  }
}

// Export singleton instance
export const enhancedWalletService = new EnhancedWalletService();
export default enhancedWalletService;
