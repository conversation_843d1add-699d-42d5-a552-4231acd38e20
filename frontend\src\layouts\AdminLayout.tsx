import React, { useEffect } from 'react';
import {
  Box,
  Container,
  useToast,
  Text,
  HStack,
  Icon,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink
} from '@chakra-ui/react';
import AdminNavbar from '../components/AdminNavbar';
import { Outlet, useLocation, Link as RouterLink } from 'react-router-dom';
import useAuth from '../hooks/useAuth';
import useAdminAuth from '../hooks/useAdminAuth';
import { FaHome, FaChevronRight } from 'react-icons/fa';

const AdminLayout = () => {
  const { user } = useAuth();
  const { isAdmin } = useAdminAuth();
  const location = useLocation();
  const toast = useToast();

  // Show welcome toast on first render - using localStorage to prevent duplicate toasts
  useEffect(() => {
    // Check if we've already shown the welcome toast in this session
    const hasShownWelcomeToast = localStorage.getItem('admin_welcome_shown');

    // Check if the toast was shown more than 24 hours ago
    const lastShownTime = localStorage.getItem('admin_welcome_timestamp');
    const now = Date.now();
    const ONE_DAY = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

    // Reset the flag if it's been more than 24 hours
    if (lastShownTime && now - parseInt(lastShownTime) > ONE_DAY) {
      localStorage.removeItem('admin_welcome_shown');
      localStorage.removeItem('admin_welcome_timestamp');
    }

    if (isAdmin && user && !hasShownWelcomeToast) {
      // Set flag to prevent showing the toast again
      localStorage.setItem('admin_welcome_shown', 'true');
      localStorage.setItem('admin_welcome_timestamp', now.toString());

      toast({
        title: "Admin Panel",
        description: `Welcome, ${user.firstName}. You have access to the admin panel.`,
        status: "info",
        duration: 3000,
        isClosable: true,
        position: "top-right",
        id: "admin-welcome-toast" // Add an ID to prevent duplicate toasts
      });
    }

    // Clear the flag when component unmounts
    return () => {
      // We don't clear the flag here anymore to prevent showing the toast on every navigation
    };
  }, [isAdmin, user, toast]);

  // Generate breadcrumbs based on current path
  const generateBreadcrumbs = () => {
    const paths = location.pathname.split('/').filter(path => path);

    // Map path segments to readable names
    const pathNames: Record<string, string> = {
      'admin': 'Admin Panel',
      'users': 'Users',
      'transactions': 'Transactions',
      'deposits': 'Deposits',
      'withdrawals': 'Withdrawals',
      'referrals': 'Referrals',
      'content': 'Content Management',
      'settings': 'System Settings',
      'home-management': 'Home Page Management',
      'profile-management': 'Profile Management',
      'commission': 'Commission Settings',
      'transaction': 'Transaction Details',
      'site-management': 'Site Management',
      'crypto-prices': 'Crypto Price Management'
    };

    return (
      <Breadcrumb
        separator={<Icon as={FaChevronRight} color="gray.500" boxSize={3} />}
        fontSize="sm"
        mb={6}
      >
        <BreadcrumbItem>
          <BreadcrumbLink as={RouterLink} to="/" color="gray.400" _hover={{ color: "#F0B90B" }}>
            <HStack>
              <Icon as={FaHome} />
              <Text>Home</Text>
            </HStack>
          </BreadcrumbLink>
        </BreadcrumbItem>

        {paths.map((path, index) => {
          // Build the accumulated path
          const href = `/${paths.slice(0, index + 1).join('/')}`;
          const isLast = index === paths.length - 1;

          return (
            <BreadcrumbItem key={path} isCurrentPage={isLast}>
              <BreadcrumbLink
                as={RouterLink}
                to={href}
                color={isLast ? "#F0B90B" : "gray.400"}
                fontWeight={isLast ? "600" : "normal"}
                _hover={{ color: "#F0B90B" }}
              >
                {pathNames[path] || path}
              </BreadcrumbLink>
            </BreadcrumbItem>
          );
        })}
      </Breadcrumb>
    );
  };

  return (
    <Box bg="#0B0E11" minH="100vh">
      <AdminNavbar />
      <Box py={8}>
        <Container maxW="container.2xl" px={{ base: 3, md: 6, lg: 8 }}>
          {/* Breadcrumbs */}
          {generateBreadcrumbs()}

          {/* Main Content */}
          <Box overflowX="auto">
            <Outlet />
          </Box>
        </Container>
      </Box>
    </Box>
  );
};

export default AdminLayout;
