import express from 'express';
import {
  adminGetAllPrices,
  adminCreateOrUpdatePrice,
  adminUpdatePrice,
  adminDeletePrice,
  adminCleanOldData,
  adminGetStatistics
} from '../../controllers/CryptoPriceController';
import {
  validateCryptoPriceData,
  validateCryptoPriceUpdate,
  validateCryptoPriceDelete,
  validateCryptoPriceQuery,
  validateCleanOldData
} from '../../middleware/cryptoPriceValidation';
import { protect, admin } from '../../middleware/authMiddleware';

const router = express.Router();

// Apply authentication and admin middleware to all routes
router.use(protect, admin);

/**
 * @route   GET /api/admin/crypto-prices
 * @desc    Get all crypto prices with pagination and filtering
 * @access  Admin
 */
router.get('/', validateCryptoPriceQuery, adminGetAllPrices);

/**
 * @route   GET /api/admin/crypto-prices/statistics
 * @desc    Get crypto price statistics
 * @access  Admin
 */
router.get('/statistics', adminGetStatistics);

/**
 * @route   POST /api/admin/crypto-prices
 * @desc    Create or update crypto price entry
 * @access  Admin
 */
router.post('/', validateCryptoPriceData, adminCreateOrUpdatePrice);

/**
 * @route   PUT /api/admin/crypto-prices/:id
 * @desc    Update existing crypto price entry
 * @access  Admin
 */
router.put('/:id', validateCryptoPriceUpdate, adminUpdatePrice);

/**
 * @route   DELETE /api/admin/crypto-prices/:id
 * @desc    Delete crypto price entry
 * @access  Admin
 */
router.delete('/:id', validateCryptoPriceDelete, adminDeletePrice);

/**
 * @route   DELETE /api/admin/crypto-prices/clean
 * @desc    Clean old crypto price data
 * @access  Admin
 */
router.delete('/clean', validateCleanOldData, adminCleanOldData);

export default router;
