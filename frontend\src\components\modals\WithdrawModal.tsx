import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  HStack,
  Text,
  Box,
  Flex,
  useToast,
  Select,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Progress,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Divider,
  Badge,
  Icon,
  Radio,
  RadioGroup,
  Stack,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription
} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { FaInfoCircle } from 'react-icons/fa';

import useWallet from '../../hooks/useWallet';
import NetworkSelector from '../common/NetworkSelector';
import { CRYPTO_NETWORKS, getDefaultNetwork, NetworkOption } from '../../utils/cryptoNetworks';
import { InvestmentPackage } from '../../services/investmentBalanceService';
import withdrawalService from '../../services/withdrawalService';
import { walletManagementService, WithdrawalAddress } from '../../services/walletManagementService';

// Phase 3: Interest-Only Withdrawal System interfaces
interface WithdrawableBalance {
  asset: string;
  principalAmount: number;
  interestAmount: number;
  commissionAmount: number;
  totalWithdrawable: number;
  principalLocked: boolean;
  principalLockUntil: Date | null;
  daysUntilUnlock: number;
  meetsMinimumThreshold: boolean;
  minimumThreshold: number;
}

interface WithdrawalEligibility {
  eligible: boolean;
  reason: string;
  details: {
    requestedAmount: number;
    availableAmount: number;
    minimumRequired: number;
    principalLocked: boolean;
    interestOnly: boolean;
  };
}

interface PrincipalLockStatus {
  packageId: string;
  locked: boolean;
  unlockDate: Date | null;
  daysRemaining: number;
  activatedAt: Date;
  lockDuration: number;
}

interface WithdrawModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialCrypto?: string; // Loại tiền tệ được chọn từ trang Home
  initialWithdrawalType?: 'interest' | 'commission' | 'principal'; // Keep frontend naming
  initialInterestAmount?: number; // Specific interest earnings amount from investment cards
  onSuccess?: () => void; // Callback when withdrawal is successful
  availableBalances?: {
    interest: { [currency: string]: number };
    commission: { [currency: string]: number };
    principal: Array<{ // Keep frontend naming
      id: string;
      amount: number;
      currency: string;
      activatedAt: Date;
      daysSinceActivation: number;
    }>;
  };
}

const WithdrawModal: React.FC<WithdrawModalProps> = ({
  isOpen,
  onClose,
  initialCrypto,
  initialWithdrawalType = 'interest',
  initialInterestAmount,
  onSuccess,
  availableBalances
}) => {
  const { t } = useTranslation();
  const toast = useToast();

  const { wallet, loading, fetchWallet } = useWallet();

  // Map frontend network IDs to backend enum values
  const mapNetworkToBackend = (networkId: string): string => {
    const networkMapping: { [key: string]: string } = {
      'bitcoin': 'Bitcoin',
      'lightning': 'Bitcoin',
      'brc20': 'Bitcoin',
      'erc20': 'Ethereum',
      'eth2': 'Ethereum',
      'trc20': 'Tron',
      'tron': 'Tron',
      'bep20': 'BSC',
      'bep2': 'BSC',
      'solana': 'Solana',
      'spl': 'Solana',
      'dogecoin': 'Bitcoin', // Dogecoin uses Bitcoin-like network
      'drc20': 'Bitcoin',
      'polygon': 'Polygon'
    };

    return networkMapping[networkId] || networkId;
  };

  // State variables
  const [activeStep, setActiveStep] = useState(0);
  const [selectedCrypto, setSelectedCrypto] = useState(initialCrypto ? mapCryptoNameToSymbol(initialCrypto) : 'BTC');
  const [amount, setAmount] = useState('');
  const [walletAddress, setWalletAddress] = useState('');
  const [memo, setMemo] = useState('');
  const [withdrawalType, setWithdrawalType] = useState<'interest' | 'commission' | 'principal'>(initialWithdrawalType || 'interest');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedNetwork, setSelectedNetwork] = useState<string>('');
  const [networkOptions, setNetworkOptions] = useState<NetworkOption[]>([]);
  const [currentNetworkDetails, setCurrentNetworkDetails] = useState<NetworkOption | undefined>();
  const [investmentPackages, setInvestmentPackages] = useState<InvestmentPackage[]>([]);
  const [loadingPackages, setLoadingPackages] = useState(false);

  // Phase 3: Interest-Only Withdrawal System state
  const [withdrawableBalance, setWithdrawableBalance] = useState<WithdrawableBalance | null>(null);
  const [withdrawalValidationError, setWithdrawalValidationError] = useState<string | null>(null);
  const [phase3ValidationEnabled, setPhase3ValidationEnabled] = useState(true);
  const [selectedInvestmentId, setSelectedInvestmentId] = useState<string>('');
  const [loadingWithdrawableBalance, setLoadingWithdrawableBalance] = useState(false);
  const [authenticationError, setAuthenticationError] = useState(false);

  // Step validation errors
  const [stepValidationError, setStepValidationError] = useState<string | null>(null);

  // Wallet Management state
  const [savedAddresses, setSavedAddresses] = useState<WithdrawalAddress[]>([]);
  const [selectedAddressId, setSelectedAddressId] = useState<string>('');
  const [isNewAddress, setIsNewAddress] = useState(false);
  const [addressLabel, setAddressLabel] = useState('');
  const [loadingAddresses, setLoadingAddresses] = useState(false);
  const [savingAddress, setSavingAddress] = useState(false);

  // Hàm chuyển đổi tên tiền tệ từ trang Home sang mã tiền tệ
  function mapCryptoNameToSymbol(cryptoName: string): string {
    // Nếu đã là symbol (như BTC, ETH), trả về luôn
    const upperCrypto = cryptoName.toUpperCase();
    if (['BTC', 'ETH', 'USDT', 'DOGE', 'TRX', 'BNB'].includes(upperCrypto)) {
      return upperCrypto;
    }

    // Nếu là tên đầy đủ, chuyển đổi
    const cryptoMap: Record<string, string> = {
      'bitcoin': 'BTC',
      'ethereum': 'ETH',
      'tether': 'USDT',
      'dogecoin': 'DOGE',
      'tron': 'TRX',
      'bnb': 'BNB'
    };
    return cryptoMap[cryptoName.toLowerCase()] || 'BTC';
  }

  // Load saved withdrawal addresses for current crypto and network
  const loadSavedAddresses = async (currency: string, network: string) => {
    if (!currency || !network) return;

    setLoadingAddresses(true);
    try {
      const response = await walletManagementService.getUserAddresses(currency);

      // Filter by network if needed
      const filteredAddresses = response.data.filter(addr =>
        addr.network === network || !addr.network // Include addresses without network for backward compatibility
      );

      setSavedAddresses(filteredAddresses);

      // Reset selection when addresses change
      setSelectedAddressId('');
      setIsNewAddress(filteredAddresses.length === 0);

      console.log('💳 Loaded saved addresses:', {
        currency,
        network,
        count: filteredAddresses.length,
        addresses: filteredAddresses.map(addr => ({
          id: addr._id,
          label: addr.label,
          address: addr.address.substring(0, 10) + '...'
        }))
      });
    } catch (error) {
      console.error('Error loading saved addresses:', error);
      setSavedAddresses([]);
      setIsNewAddress(true);
    } finally {
      setLoadingAddresses(false);
    }
  };

  // Save new withdrawal address
  const saveNewAddress = async (currency: string, network: string, address: string, label: string) => {
    setSavingAddress(true);
    try {
      const response = await walletManagementService.addAddress({
        currency,
        network,
        address,
        label: label || `${currency} Address`
      });

      const newAddress = response.data;

      // Add to saved addresses list
      setSavedAddresses(prev => [...prev, newAddress]);

      // Select the new address
      setSelectedAddressId(newAddress._id);
      setIsNewAddress(false);

      toast({
        title: 'Address Saved',
        description: `${currency} address saved successfully`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      console.log('💾 Saved new address:', {
        currency,
        network,
        label,
        addressId: newAddress._id
      });

      return newAddress;
    } catch (error) {
      console.error('Error saving address:', error);
      toast({
        title: 'Save Failed',
        description: 'Failed to save withdrawal address',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      throw error;
    } finally {
      setSavingAddress(false);
    }
  };

  // Phase 3: Fetch withdrawable balance using new API
  const fetchWithdrawableBalance = async (asset: string) => {
    if (!phase3ValidationEnabled) return;

    setLoadingWithdrawableBalance(true);
    setWithdrawalValidationError(null);

    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/wallets/withdrawable-balance/${asset}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // Check if response is HTML (error page) instead of JSON
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('text/html')) {
        console.error('Received HTML response instead of JSON for withdrawable balance');
        setAuthenticationError(true);
        setPhase3ValidationEnabled(false);
        throw new Error('Server returned HTML instead of JSON - possible authentication issue');
      }

      if (!response.ok) {
        // Handle authentication errors
        if (response.status === 401) {
          console.error('Authentication failed for withdrawable balance');
          setAuthenticationError(true);
          setPhase3ValidationEnabled(false);
          throw new Error('Authentication required - please login again');
        }

        // Try to parse error response
        let errorMessage = `Failed to fetch withdrawable balance: ${response.statusText}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (parseError) {
          console.error('Could not parse error response:', parseError);
        }
        throw new Error(errorMessage);
      }

      const data = await response.json();

      if (data.status === 'success' && data.data.withdrawableBalance) {
        setWithdrawableBalance(data.data.withdrawableBalance);
        console.log('Phase 3 withdrawable balance loaded:', data.data.withdrawableBalance);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error fetching withdrawable balance:', error);
      setWithdrawalValidationError(error instanceof Error ? error.message : 'Failed to load withdrawal data');
      // Fallback to legacy system
      setPhase3ValidationEnabled(false);
    } finally {
      setLoadingWithdrawableBalance(false);
    }
  };

  // Phase 3: Validate withdrawal eligibility
  const validateWithdrawalEligibility = async (asset: string, amount: number): Promise<WithdrawalEligibility | null> => {
    if (!phase3ValidationEnabled) return null;

    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/withdrawals/validate`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          cryptocurrency: asset,
          amount,
          walletAddress: 'validation-check', // Dummy address for validation
          withdrawalType: withdrawalType === 'principal' ? 'balance' : withdrawalType, // ✅ Map principal to balance
          network: mapNetworkToBackend(selectedNetwork), // ✅ Map network ID to backend enum
          investmentPackageId: withdrawalType === 'principal' ? selectedInvestmentId : undefined
        }),
      });

      // Check if response is HTML (error page) instead of JSON
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('text/html')) {
        console.error('Received HTML response instead of JSON for withdrawal validation');
        throw new Error('Server returned HTML instead of JSON - possible authentication issue');
      }

      const data = await response.json();

      if (!response.ok) {
        // Extract validation error details
        if (data.withdrawalValidation) {
          return {
            eligible: false,
            reason: data.message || 'Withdrawal validation failed',
            details: data.withdrawalValidation
          };
        }
        throw new Error(data.message || 'Validation failed');
      }

      return {
        eligible: true,
        reason: 'Withdrawal eligible',
        details: {
          requestedAmount: amount,
          availableAmount: withdrawableBalance?.totalWithdrawable || 0,
          minimumRequired: withdrawableBalance?.minimumThreshold || 50,
          principalLocked: withdrawableBalance?.principalLocked || false,
          interestOnly: withdrawableBalance?.principalLocked || false
        }
      };
    } catch (error) {
      console.error('Error validating withdrawal eligibility:', error);
      return null;
    }
  };

  // Initialize network options when cryptocurrency changes
  useEffect(() => {
    const networks = CRYPTO_NETWORKS[selectedCrypto] || [];
    setNetworkOptions(networks);

    const defaultNetwork = getDefaultNetwork(selectedCrypto);
    if (defaultNetwork) {
      setSelectedNetwork(defaultNetwork.id);
      setCurrentNetworkDetails(defaultNetwork);
    }
  }, [selectedCrypto]);

  // Update current network details when selected network changes
  useEffect(() => {
    if (selectedNetwork && networkOptions.length > 0) {
      const networkDetails = networkOptions.find(network => network.id === selectedNetwork);
      setCurrentNetworkDetails(networkDetails);
    }
  }, [selectedNetwork, networkOptions]);

  // Reset amount when crypto or withdrawal type changes to avoid invalid amounts
  useEffect(() => {
    setAmount('');
    setStepValidationError(null); // Clear validation errors when crypto/type changes
  }, [selectedCrypto, withdrawalType]);

  // Phase 3: Load withdrawable balance when crypto changes or modal opens
  useEffect(() => {
    if (selectedCrypto && phase3ValidationEnabled && isOpen) {
      console.log('🔄 Fetching withdrawable balance for:', selectedCrypto);
      fetchWithdrawableBalance(selectedCrypto);
    }
  }, [selectedCrypto, phase3ValidationEnabled, isOpen]);

  // Note: loadSavedAddresses is now called when completing step 1 instead of useEffect

  // Phase 3: Real-time validation when amount changes
  useEffect(() => {
    const validateAmount = async () => {
      if (amount && parseFloat(amount) > 0 && selectedCrypto && phase3ValidationEnabled) {
        const validation = await validateWithdrawalEligibility(selectedCrypto, parseFloat(amount));
        if (validation && !validation.eligible) {
          setWithdrawalValidationError(validation.reason);
        } else {
          setWithdrawalValidationError(null);
        }
      } else {
        setWithdrawalValidationError(null);
      }
    };

    const timeoutId = setTimeout(validateAmount, 500); // Debounce validation
    return () => clearTimeout(timeoutId);
  }, [amount, selectedCrypto, withdrawalType, selectedNetwork, phase3ValidationEnabled]);

  // Load investment packages when modal opens (for principal withdrawals)
  useEffect(() => {
    const loadInvestmentPackages = async () => {
      if (isOpen) {
        setLoadingPackages(true);
        try {
          // Only load investment packages for principal withdrawals
          // Interest and commission balances come from /api/wallets/info via useWallet hook
          const packagesResponse = await fetch(`${import.meta.env.VITE_API_URL}/investment-packages/packages?status=active&limit=100`, {
            method: 'GET',
            credentials: 'include',
            headers: {
              'Content-Type': 'application/json',
            },
          });

          // Check if response is HTML (error page) instead of JSON
          const contentType = packagesResponse.headers.get('content-type');
          if (contentType && contentType.includes('text/html')) {
            console.error('Received HTML response instead of JSON for investment packages');
            throw new Error('Server returned HTML instead of JSON - possible authentication issue');
          }

          if (packagesResponse.ok) {
            const packagesData = await packagesResponse.json();
            if (packagesData.status === 'success' && packagesData.data?.packages) {
              setInvestmentPackages(packagesData.data.packages);
              console.log('📦 Loaded investment packages with activeDays:', packagesData.data.packages.map((pkg: any) => ({
                id: pkg._id,
                currency: pkg.currency,
                amount: pkg.amount,
                activeDays: pkg.activeDays,
                status: pkg.status
              })));
            } else {
              setInvestmentPackages([]);
            }
          } else {
            // Try to parse error response
            let errorMessage = `Failed to fetch investment packages: ${packagesResponse.statusText}`;
            try {
              const errorData = await packagesResponse.json();
              errorMessage = errorData.message || errorMessage;
            } catch (parseError) {
              console.error('Could not parse error response:', parseError);
            }
            console.error(errorMessage);
            setInvestmentPackages([]);
          }
        } catch (error) {
          console.error('Error loading investment packages:', error);
          setInvestmentPackages([]);
        } finally {
          setLoadingPackages(false);
        }
      }
    };

    loadInvestmentPackages();
  }, [isOpen]);

  // Update selectedCrypto and withdrawalType when modal opens
  useEffect(() => {
    if (isOpen) {
      console.log('🔄 WithdrawModal opened with props:', {
        initialCrypto,
        initialWithdrawalType,
        initialInterestAmount,
        isOpen
      });

      if (initialCrypto) {
        const cryptoSymbol = mapCryptoNameToSymbol(initialCrypto);
        console.log('💰 Setting crypto symbol:', { initialCrypto, cryptoSymbol });
        setSelectedCrypto(cryptoSymbol);

        // Refresh withdrawable balance when modal opens with crypto
        if (phase3ValidationEnabled) {
          console.log('🔄 Refreshing withdrawable balance on modal open for:', cryptoSymbol);
          fetchWithdrawableBalance(cryptoSymbol);
        }
      }
      if (initialWithdrawalType) {
        setWithdrawalType(initialWithdrawalType);
      }

      // Refresh wallet data to get latest balances
      if (fetchWallet) {
        console.log('🔄 Refreshing wallet data on modal open');
        fetchWallet();
      }

      // Reset form when opening
      setAmount('');
      setWalletAddress('');
      setMemo('');
      setActiveStep(0);
      setIsSubmitting(false);
      setUploadProgress(0);
      setStepValidationError(null); // Clear validation errors when modal opens
      setAuthenticationError(false); // Reset authentication error when modal opens
      setPhase3ValidationEnabled(true); // Re-enable Phase 3 validation when modal opens
    }
  }, [isOpen, initialCrypto, initialWithdrawalType, fetchWallet, phase3ValidationEnabled]);

  // Reset form when modal is closed
  useEffect(() => {
    if (!isOpen) {
      setAmount('');
      setWalletAddress('');
      setMemo('');
      setActiveStep(0);
      setIsSubmitting(false);
      setUploadProgress(0);
      // Reset wallet management state
      setSavedAddresses([]);
      setSelectedAddressId('');
      setIsNewAddress(false);
      setAddressLabel('');
      setLoadingAddresses(false);
      setSavingAddress(false);
    }
  }, [isOpen]);

  // Handle network selection change
  const handleNetworkChange = (networkId: string) => {
    setSelectedNetwork(networkId);
    setStepValidationError(null); // Clear validation error when network changes
  };

  // Get withdrawal fee based on selected network
  const getWithdrawalFee = () => {
    if (currentNetworkDetails) {
      return currentNetworkDetails.fee;
    }

    // Fallback to default fees if network details are not available
    switch(selectedCrypto) {
      case 'BTC': return 0.0005;
      case 'ETH': return 0.01;
      case 'USDT': return 2.00;
      case 'DOGE': return 1.00;
      case 'XRP': return 0.5;
      default: return 0.001;
    }
  };

  // Get minimum withdrawal amount based on selected cryptocurrency
  const getMinWithdrawal = () => {
    // Phase 3: Use minimum threshold from withdrawable balance if available
    if (phase3ValidationEnabled && withdrawableBalance) {
      return withdrawableBalance.minimumThreshold;
    }

    // Fallback to legacy minimums (updated for 20 USD equivalent)
    switch(selectedCrypto) {
      case 'BTC': return 0.0005; // ~20 USD at 40k price
      case 'ETH': return 0.008;  // ~20 USD at 2.5k price
      case 'USDT': return 20.00; // 20 USD
      case 'DOGE': return 200.00; // ~20 USD at 0.1 price
      case 'XRP': return 40; // ~20 USD at 0.5 price
      default: return 0.001;
    }
  };

  // Get precision for formatting based on selected cryptocurrency
  const getPrecision = () => {
    switch(selectedCrypto) {
      case 'BTC': return 8;
      case 'ETH': return 6;
      case 'USDT': return 2;
      case 'DOGE': return 2;
      case 'XRP': return 2;
      default: return 2;
    }
  };

  // Format amount with appropriate precision
  const formatAmount = (value: number) => {
    return value.toFixed(getPrecision());
  };

  // Get processing time based on selected network
  const getProcessingTime = () => {
    if (currentNetworkDetails) {
      return currentNetworkDetails.processingTime;
    }
    return '1 day'; // Default processing time
  };

  // Get balance for specific crypto and type using /api/wallets/info data
  const getBalanceForCryptoAndType = (crypto: string, type: 'interest' | 'commission' | 'balance') => {
    console.log(`Getting balance for ${crypto} (${type}):`, {
      wallet: wallet?.assets,
      loading
    });

    // Use wallet data from /api/wallets/info
    if (wallet && wallet.assets && wallet.assets.length > 0) {
      const asset = wallet.assets.find(a => a.symbol === crypto);
      if (asset) {
        let balance = 0;
        switch (type) {
          case 'interest':
            // Use interestBalance from /api/wallets/info
            balance = asset.interestBalance || 0;
            break;
          case 'commission':
            // Use commissionBalance from /api/wallets/info
            balance = asset.commissionBalance || 0;
            break;
          case 'balance':
            // Use balance field from /api/wallets/info (main principal amount)
            balance = asset.balance || 0;
            break;
          default:
            balance = 0;
        }

        console.log(`Wallet balance for ${crypto} (${type}):`, {
          balance: asset.balance,
          interestBalance: asset.interestBalance,
          commissionBalance: asset.commissionBalance,
          calculatedBalance: balance,
          source: '/api/wallets/info'
        });

        return balance;
      }
    }

    // If loading, return 0
    if (loading) {
      console.log(`Still loading balances for ${crypto}`);
      return 0;
    }

    console.log(`No balance found for ${crypto} (${type})`);
    return 0;
  };

  // Get available balance based on selected cryptocurrency and withdrawal type
  const getAvailableBalance = () => {
    // Phase 3: Use withdrawable balance if available
    if (phase3ValidationEnabled && withdrawableBalance) {
      if (withdrawalType === 'interest') {
        // During principal lock, only interest + commission are withdrawable
        if (withdrawableBalance.principalLocked) {
          return withdrawableBalance.interestAmount + withdrawableBalance.commissionAmount;
        }
        // After lock expires, total amount is withdrawable
        return withdrawableBalance.totalWithdrawable;
      } else if (withdrawalType === 'commission') {
        return withdrawableBalance.commissionAmount;
      } else if (withdrawalType === 'principal') {
        // For principal withdrawals, check if locked
        if (withdrawableBalance.principalLocked) {
          return 0; // Principal is locked, cannot withdraw
        }
        return withdrawableBalance.principalAmount;
      }
    }

    // For interest and commission withdrawals, use /api/wallets/info data
    if (withdrawalType === 'interest' || withdrawalType === 'commission') {
      return getBalanceForCryptoAndType(selectedCrypto, withdrawalType);
    }

    // For principal withdrawals, use wallet balance field (main balance)
    if (withdrawalType === 'principal') {
      // Use balance field from wallet asset (main principal amount)
      const mainBalance = getBalanceForCryptoAndType(selectedCrypto, 'balance');

      console.log('💰 Available balance for principal withdrawal:', {
        selectedCrypto,
        mainBalance,
        source: '/api/wallets/info assets.balance'
      });

      return mainBalance;
    }

    return 0;
  };

  // Next step in the wizard
  const nextStep = async () => {
    // Clear previous validation errors
    setStepValidationError(null);

    if (activeStep === 0) {
      // Validate first step
      if (!amount || parseFloat(amount) <= 0) {
        const errorMsg = t('withdrawModal.enterValidAmount', 'Please enter a valid amount.');
        setStepValidationError(errorMsg);
        toast({
          title: t('withdrawModal.invalidAmount', 'Invalid Amount'),
          description: errorMsg,
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      const availableBalance = getAvailableBalance();
      if (parseFloat(amount) > availableBalance) {
        const errorMsg = t('withdrawModal.insufficientBalanceDesc', 'The amount you want to withdraw exceeds your balance.');
        setStepValidationError(errorMsg);
        toast({
          title: t('withdrawModal.insufficientBalance', 'Insufficient Balance'),
          description: errorMsg,
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      if (parseFloat(amount) < getMinWithdrawal()) {
        const errorMsg = `Minimum withdrawal amount: ${formatAmount(getMinWithdrawal())} ${selectedCrypto}`;
        setStepValidationError(errorMsg);
        toast({
          title: t('withdrawModal.belowMinimum', 'Below Minimum Withdrawal Amount'),
          description: errorMsg,
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      // Validate network selection
      if (!selectedNetwork) {
        const errorMsg = t('withdrawModal.selectNetwork', 'Please select a network for your withdrawal.');
        setStepValidationError(errorMsg);
        toast({
          title: t('withdrawModal.networkRequired', 'Network Required'),
          description: errorMsg,
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      // Validate investment package selection for principal withdrawals
      if (withdrawalType === 'principal' && !selectedInvestmentId) {
        const errorMsg = t('withdrawModal.selectInvestment', 'Please select an investment package to withdraw from.');
        setStepValidationError(errorMsg);
        toast({
          title: t('withdrawModal.investmentRequired', 'Investment Package Required'),
          description: errorMsg,
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      // Phase 3: Additional validation using withdrawable balance API
      if (phase3ValidationEnabled && selectedCrypto) {
        try {
          const validation = await validateWithdrawalEligibility(selectedCrypto, parseFloat(amount));
          if (validation && !validation.eligible) {
            setStepValidationError(validation.reason);
            toast({
              title: t('withdrawModal.validationFailed', 'Withdrawal Validation Failed'),
              description: validation.reason,
              status: 'error',
              duration: 5000,
              isClosable: true,
            });
            return;
          }
        } catch (error) {
          console.error('Error validating withdrawal eligibility:', error);
          const errorMsg = t('withdrawModal.validationErrorDesc', 'Unable to validate withdrawal. Please try again.');
          setStepValidationError(errorMsg);
          toast({
            title: t('withdrawModal.validationError', 'Validation Error'),
            description: errorMsg,
            status: 'error',
            duration: 3000,
            isClosable: true,
          });
          return;
        }
      }
    } else if (activeStep === 1) {
      // Validate second step
      if (!walletAddress) {
        toast({
          title: t('withdrawModal.addressRequired', 'Wallet Address Required'),
          description: t('withdrawModal.enterAddress', 'Please enter a valid wallet address.'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      // Save new address if it's a new one
      if (isNewAddress && walletAddress && !savingAddress) {
        try {
          await saveNewAddress(selectedCrypto, selectedNetwork, walletAddress, addressLabel);
        } catch (error) {
          // Error is already handled in saveNewAddress function
          return;
        }
      }
    }

    // Load saved addresses when completing step 1 (moving to step 2)
    if (activeStep === 0 && selectedCrypto && selectedNetwork) {
      console.log('🔄 Loading saved addresses for step 2:', { selectedCrypto, selectedNetwork });
      await loadSavedAddresses(selectedCrypto, selectedNetwork);
    }

    setActiveStep(activeStep + 1);
  };

  // Previous step in the wizard
  const prevStep = () => {
    setActiveStep(activeStep - 1);
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Validate form
    if (!amount || parseFloat(amount) <= 0) {
      toast({
        title: t('withdrawModal.invalidAmount', 'Invalid Amount'),
        description: t('withdrawModal.enterValidAmount', 'Please enter a valid amount.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const availableBalance = getAvailableBalance();
    if (parseFloat(amount) > availableBalance) {
      toast({
        title: t('withdrawModal.insufficientBalance', 'Insufficient Balance'),
        description: t('withdrawModal.insufficientBalanceDesc', 'The amount you want to withdraw exceeds your balance.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (parseFloat(amount) < getMinWithdrawal()) {
      toast({
        title: t('withdrawModal.belowMinimum', 'Below Minimum Withdrawal Amount'),
        description: `Minimum withdrawal amount: ${formatAmount(getMinWithdrawal())} ${selectedCrypto}`,
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // Validate network selection
    if (!selectedNetwork) {
      toast({
        title: t('withdrawModal.networkRequired', 'Network Required'),
        description: t('withdrawModal.selectNetwork', 'Please select a network for your withdrawal.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (!walletAddress) {
      toast({
        title: t('withdrawModal.addressRequired', 'Wallet Address Required'),
        description: t('withdrawModal.enterAddress', 'Please enter a valid wallet address.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // Start submission process
    setIsSubmitting(true);
    setUploadProgress(0);

    try {
      // Simulate progress for better UX
      const interval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(interval);
            return 90;
          }
          return prev + 10;
        });
      }, 300);

      // Send withdrawal request to backend
      const withdrawalData = {
        asset: selectedCrypto,
        amount: parseFloat(amount),
        address: walletAddress,
        memo: memo || undefined,
        withdrawalType: withdrawalType === 'principal' ? 'balance' : withdrawalType, // ✅ Map principal to balance
        network: mapNetworkToBackend(selectedNetwork), // ✅ Map network ID to backend enum
        blockchainNetwork: selectedNetwork, // Keep original for any other backend compatibility
        ...(withdrawalType === 'principal' && selectedInvestmentId && { investmentId: selectedInvestmentId })
      };

      // Use the correct withdrawal endpoint
      const response = await fetch(`${import.meta.env.VITE_API_URL}/withdrawals/submit`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          cryptocurrency: withdrawalData.asset,
          amount: withdrawalData.amount,
          walletAddress: withdrawalData.address,
          withdrawalType: withdrawalData.withdrawalType,
          network: withdrawalData.network,
          ...(withdrawalData.investmentId && { investmentPackageId: withdrawalData.investmentId })
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Withdrawal failed');
      }

      // Complete progress
      clearInterval(interval);
      setUploadProgress(100);

      toast({
        title: t('withdrawModal.successTitle', 'Withdrawal Successful'),
        description: t('withdrawModal.successDescription', 'Your withdrawal request has been received. Your wallet balance has been updated immediately.'),
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      // Refresh wallet data to show updated balances
      await fetchWallet();

      // Refresh withdrawable balance to show updated amounts
      if (selectedCrypto && phase3ValidationEnabled) {
        console.log('🔄 Refreshing withdrawable balance after successful withdrawal');
        await fetchWithdrawableBalance(selectedCrypto);
      }

      // Call onSuccess callback if provided
      onSuccess?.();

      // Reset form and close modal
      setAmount('');
      setWalletAddress('');
      setMemo('');
      setActiveStep(0);
      onClose();
    } catch (error: unknown) {
      console.error('Withdrawal error:', error);

      let errorMessage = t('withdrawModal.errorDescription', 'An error occurred while processing your withdrawal.');
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null && 'response' in error &&
                error.response && typeof error.response === 'object' &&
                'data' in error.response && error.response.data &&
                typeof error.response.data === 'object' && 'message' in error.response.data) {
        errorMessage = (error.response.data as { message: string }).message;
      }

      toast({
        title: t('withdrawModal.errorTitle', 'Withdrawal Failed'),
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md" isCentered>
      <ModalOverlay backdropFilter="blur(5px)" />
      <ModalContent
        bg="#0B0E11"
        borderColor="#2B3139"
        borderWidth="1px"
        mx={{ base: 2, md: "auto" }}
        maxH={{ base: "calc(100vh - 40px)", md: "calc(100vh - 80px)" }}
        overflowY="auto"
        height="auto"
      >
        <ModalHeader color="#F0B90B" borderBottomWidth="1px" borderColor="#2B3139">
          {t('withdrawModal.title', 'Withdrawal Transaction')}
        </ModalHeader>
        <ModalCloseButton color="#EAECEF" />

        <ModalBody py={6}>
          <Tabs index={activeStep} onChange={setActiveStep} variant="unstyled" colorScheme="yellow">
            <TabList mb={4}>
              <Tab
                _selected={{ color: "#F0B90B", fontWeight: "bold" }}
                color={activeStep === 0 ? "#F0B90B" : "#848E9C"}
                fontWeight={activeStep === 0 ? "bold" : "normal"}
              >
                1. Amount
              </Tab>
              <Tab
                _selected={{ color: "#F0B90B", fontWeight: "bold" }}
                color={activeStep === 1 ? "#F0B90B" : "#848E9C"}
                fontWeight={activeStep === 1 ? "bold" : "normal"}
                isDisabled={activeStep < 1}
              >
                2. Address
              </Tab>
              <Tab
                _selected={{ color: "#F0B90B", fontWeight: "bold" }}
                color={activeStep === 2 ? "#F0B90B" : "#848E9C"}
                fontWeight={activeStep === 2 ? "bold" : "normal"}
                isDisabled={activeStep < 2}
              >
                3. Confirm
              </Tab>
            </TabList>

            <TabPanels>
              {/* Step 1: Amount Details */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  {/* Step Validation Error Display */}
                  {stepValidationError && (
                    <Alert status="error" borderRadius="md" bg="#F8496022">
                      <AlertIcon color="#F84960" />
                      <Box>
                        <AlertTitle color="#F84960">Validation Error</AlertTitle>
                        <AlertDescription color="#EAECEF" fontSize="sm">
                          {stepValidationError}
                        </AlertDescription>
                      </Box>
                    </Alert>
                  )}

                  <Box p={4} bg="#1E2329" borderRadius="md">
                    <Text color="#F0B90B" fontSize="md" fontWeight="bold" mb={3}>Select Withdrawal Type</Text>

                    {/* Phase 3: Principal Lock Status Display */}
                    {phase3ValidationEnabled && withdrawableBalance && withdrawableBalance.principalLocked && (
                      <Alert status="warning" borderRadius="md" mb={4} bg="#F0B90B22">
                        <AlertIcon color="#F0B90B" />
                        <Box>
                          <AlertTitle color="#F0B90B">Principal Lock Active</AlertTitle>
                          <AlertDescription color="#EAECEF" fontSize="sm">
                            Your principal amount ({withdrawableBalance.principalAmount.toFixed(2)} {selectedCrypto}) is locked for {withdrawableBalance.daysUntilUnlock} more days.
                            Only interest earnings and commission are withdrawable during this period.
                          </AlertDescription>
                          {withdrawableBalance.principalLockUntil && (
                            <Text fontSize="xs" color="#848E9C" mt={1}>
                              Principal unlocks on: {new Date(withdrawableBalance.principalLockUntil).toLocaleDateString()}
                            </Text>
                          )}
                        </Box>
                      </Alert>
                    )}

                    {/* Authentication Error Display */}
                    {authenticationError && (
                      <Alert status="warning" borderRadius="md" mb={4} bg="#F0B90B22">
                        <AlertIcon color="#F0B90B" />
                        <Box>
                          <AlertTitle color="#F0B90B">Authentication Required</AlertTitle>
                          <AlertDescription color="#EAECEF" fontSize="sm">
                            Please refresh the page and login again to access withdrawal features.
                            Advanced validation features have been disabled.
                          </AlertDescription>
                        </Box>
                      </Alert>
                    )}

                    {/* Phase 3: Validation Error Display */}
                    {withdrawalValidationError && (
                      <Alert status="error" borderRadius="md" mb={4} bg="#F8496022">
                        <AlertIcon color="#F84960" />
                        <Box>
                          <AlertTitle color="#F84960">Withdrawal Validation Error</AlertTitle>
                          <AlertDescription color="#EAECEF" fontSize="sm">
                            {withdrawalValidationError}
                          </AlertDescription>
                        </Box>
                      </Alert>
                    )}

                    <RadioGroup onChange={(value) => setWithdrawalType(value as 'interest' | 'commission' | 'principal')} value={withdrawalType} mb={4}>
                      <Stack direction="column" spacing={3}>

                        <Radio
                          value="interest"
                          colorScheme="yellow"
                          borderColor="#2B3139"
                        >
                          <VStack align="stretch" w="100%" spacing={2}>
                            <HStack justify="space-between" w="100%">
                              <HStack spacing={3}>
                                <Text color="#EAECEF" minW="140px">Interest Earnings</Text>
                                <Badge colorScheme="green">Available</Badge>
                              </HStack>
                              <Text color="#0ECB81" fontWeight="bold">
                                {loading ? '...' : (() => {
                                  // Use interestBalance from /api/wallets/info
                                  const interestBalance = getBalanceForCryptoAndType(selectedCrypto, 'interest');

                                  console.log('💰 Interest earnings display logic:', {
                                    selectedCrypto,
                                    interestBalance,
                                    source: '/api/wallets/info assets.interestBalance'
                                  });

                                  return formatAmount(interestBalance);
                                })()} {selectedCrypto}
                              </Text>
                            </HStack>
                          </VStack>
                        </Radio>

                        <Radio
                          value="commission"
                          colorScheme="yellow"
                          borderColor="#2B3139"
                        >
                          <HStack justify="space-between" w="100%">
                            <HStack spacing={3}>
                              <Text color="#EAECEF" minW="140px">Referral Commission</Text>
                              <Badge colorScheme="green">Available</Badge>
                            </HStack>
                            <Text color="#0ECB81" fontWeight="bold">
                              {loading ? '...' : (() => {
                                // Use commissionBalance from /api/wallets/info
                                const commissionBalance = getBalanceForCryptoAndType(selectedCrypto, 'commission');

                                console.log('💰 Commission earnings display logic:', {
                                  selectedCrypto,
                                  commissionBalance,
                                  source: '/api/wallets/info assets.commissionBalance'
                                });

                                return formatAmount(commissionBalance);
                              })()} {selectedCrypto}
                            </Text>
                          </HStack>
                        </Radio>

                        <Radio
                          value="principal"
                          colorScheme="yellow"
                          borderColor="#2B3139"
                        >
                          <HStack justify="space-between" w="100%">
                            <HStack spacing={3}>
                              <Text color="#EAECEF" minW="140px">Main Balance</Text>
                              <Badge colorScheme={
                                phase3ValidationEnabled && withdrawableBalance && withdrawableBalance.principalLocked
                                  ? "red"
                                  : "green"
                              }>
                                {phase3ValidationEnabled && withdrawableBalance && withdrawableBalance.principalLocked
                                  ? "Locked"
                                  : "Available"
                                }
                              </Badge>
                            </HStack>
                            <Text color={
                              phase3ValidationEnabled && withdrawableBalance && withdrawableBalance.principalLocked
                                ? "#F6465D"
                                : "#0ECB81"
                            } fontWeight="bold">
                              {loading ? '...' : (() => {
                                if (phase3ValidationEnabled && withdrawableBalance) {
                                  return formatAmount(withdrawableBalance.principalAmount);
                                }

                                // Use balance field from wallet asset (main principal amount)
                                const mainBalance = getBalanceForCryptoAndType(selectedCrypto, 'balance');

                                console.log('💰 Main Balance display logic:', {
                                  selectedCrypto,
                                  mainBalance,
                                  source: '/api/wallets/info assets.balance'
                                });

                                return formatAmount(mainBalance);
                              })()} {selectedCrypto}
                            </Text>
                          </HStack>
                        </Radio>
                      </Stack>
                    </RadioGroup>

                    {/* Investment Package Selection - Only show when principal is selected */}
                    {withdrawalType === 'principal' && (
                      <FormControl mt={4}>
                        <FormLabel color="#848E9C" fontSize="sm">Select Investment Package</FormLabel>
                        <Text color="#848E9C" fontSize="xs" mb={2}>
                          Select which investment package to withdraw from (required for tracking purposes)
                        </Text>
                        <Select
                          value={selectedInvestmentId}
                          onChange={(e) => {
                            setSelectedInvestmentId(e.target.value);
                            setStepValidationError(null); // Clear validation error when investment changes
                          }}
                          placeholder="Choose an investment package to withdraw from"
                          bg="#0B0E11"
                          borderColor="#2B3139"
                          color="#EAECEF"
                          _hover={{ borderColor: "#F0B90B" }}
                        >
                          {(() => {
                            // Get available principal investments for selected crypto
                            const principalInvestments = availableBalances?.principal?.filter(
                              inv => inv.currency === selectedCrypto && inv.daysSinceActivation >= 30
                            ) || [];

                            // Get from investment packages if availableBalances is not available
                            const eligiblePackages = Array.isArray(investmentPackages) ? investmentPackages.filter((pkg: any) => {
                              // Use activeDays from API instead of manual calculation
                              return pkg.currency === selectedCrypto && pkg.activeDays >= 30 && pkg.status === 'active';
                            }) : [];

                            // Use availableBalances if available, otherwise use investment packages
                            const allInvestments = principalInvestments.length > 0
                              ? principalInvestments.map(inv => ({
                                  id: inv.id,
                                  amount: inv.amount,
                                  currency: inv.currency,
                                  daysSinceActivation: inv.daysSinceActivation
                                }))
                              : eligiblePackages.map((pkg: any) => {
                                  // Use activeDays from API instead of manual calculation
                                  return {
                                    id: pkg._id,
                                    amount: pkg.amount,
                                    currency: pkg.currency,
                                    daysSinceActivation: pkg.activeDays // Use activeDays from API
                                  };
                                });

                            if (allInvestments.length === 0) {
                              return (
                                <option disabled>No eligible investment packages found</option>
                              );
                            }

                            return allInvestments.map((investment) => (
                              <option key={investment.id} value={investment.id}>
                                {formatAmount(investment.amount)} {investment.currency} - {investment.daysSinceActivation} days old
                                {investment.daysSinceActivation < 30 ? ' (Locked)' : ''}
                              </option>
                            ));
                          })()}
                        </Select>
                        <Text color="#848E9C" fontSize="xs" mt={1}>
                          Investment package selection is required for balance withdrawals (for tracking purposes)
                        </Text>
                        {phase3ValidationEnabled && withdrawableBalance && withdrawableBalance.principalLocked && (
                          <Text color="#F6465D" fontSize="xs" mt={1}>
                            Principal withdrawals are currently locked for {withdrawableBalance.daysUntilUnlock} more days
                          </Text>
                        )}
                      </FormControl>
                    )}

                    <Divider borderColor="#2B3139" my={3} />

                    <FormControl isRequired mt={3}>
                      <FormLabel color="#848E9C" fontSize="sm">Cryptocurrency</FormLabel>
                      <Select
                        value={selectedCrypto}
                        onChange={(e) => setSelectedCrypto(e.target.value)}
                        bg="#0B0E11"
                        borderColor="#2B3139"
                        color="#EAECEF"
                        _hover={{ borderColor: "#F0B90B" }}
                      >
                        <option value="BTC">Bitcoin (BTC)</option>
                        <option value="ETH">Ethereum (ETH)</option>
                        <option value="USDT">Tether (USDT)</option>
                        <option value="TRX">Tron (TRX)</option>
                        <option value="BNB">Binance Coin (BNB)</option>
                        <option value="DOGE">Dogecoin (DOGE)</option>
                        <option value="SOL">Solana (SOL)</option>
                      </Select>
                    </FormControl>

                    {/* Network Selection */}
                    <NetworkSelector
                      networks={networkOptions}
                      selectedNetwork={selectedNetwork}
                      onChange={handleNetworkChange}
                      isRequired={true}
                      label={t('withdrawModal.network', 'Select Network')}
                      helperText={t('withdrawModal.networkHelperText', `Choose the network for your ${selectedCrypto} withdrawal`)}
                      currency={selectedCrypto}
                    />

                    <FormControl isRequired mt={4}>
                      <FormLabel color="#848E9C" fontSize="sm">
                        Withdrawal Amount
                        <Text as="span" color="#0ECB81" fontSize="xs" ml={2}>
                          (Available: {loading ? '...' : formatAmount(getAvailableBalance())} {selectedCrypto})
                        </Text>
                      </FormLabel>
                      <NumberInput
                        value={amount}
                        onChange={(value) => {
                          setAmount(value);
                          setStepValidationError(null); // Clear validation error when amount changes
                        }}
                        max={getAvailableBalance()}
                        min={getMinWithdrawal()}
                        precision={getPrecision()}
                        w="100%"
                        isDisabled={getAvailableBalance() <= 0}
                      >
                        <NumberInputField
                          placeholder={getAvailableBalance() <= 0 ? "No balance available" : "0.00"}
                          bg="#0B0E11"
                          borderColor="#2B3139"
                          color="#EAECEF"
                          _hover={{ borderColor: "#F0B90B" }}
                        />
                        <NumberInputStepper>
                          <NumberIncrementStepper color="#EAECEF" />
                          <NumberDecrementStepper color="#EAECEF" />
                        </NumberInputStepper>
                      </NumberInput>
                      {getAvailableBalance() <= 0 && !loading && (
                        <Text color="#F6465D" fontSize="xs" mt={1}>
                          No {withdrawalType === 'interest' ? 'interest earnings' : 'referral commission'} available for {selectedCrypto}
                        </Text>
                      )}
                    </FormControl>

                    <HStack spacing={2} mt={3}>
                      <Button
                        size="sm"
                        onClick={() => setAmount(formatAmount(getAvailableBalance() * 0.25))}
                        variant="outline"
                        borderColor="#2B3139"
                        color="#848E9C"
                        _hover={{ borderColor: "#F0B90B" }}
                        isDisabled={getAvailableBalance() <= 0}
                      >
                        25%
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => setAmount(formatAmount(getAvailableBalance() * 0.5))}
                        variant="outline"
                        borderColor="#2B3139"
                        color="#848E9C"
                        _hover={{ borderColor: "#F0B90B" }}
                        isDisabled={getAvailableBalance() <= 0}
                      >
                        50%
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => setAmount(formatAmount(getAvailableBalance() * 0.75))}
                        variant="outline"
                        borderColor="#2B3139"
                        color="#848E9C"
                        _hover={{ borderColor: "#F0B90B" }}
                        isDisabled={getAvailableBalance() <= 0}
                      >
                        75%
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => setAmount(formatAmount(getAvailableBalance()))}
                        variant="outline"
                        borderColor="#2B3139"
                        color="#848E9C"
                        _hover={{ borderColor: "#F0B90B" }}
                        isDisabled={getAvailableBalance() <= 0}
                      >
                        100%
                      </Button>
                    </HStack>
                  </Box>

                  <Box p={4} bg="#1E2329" borderRadius="md">
                    <Flex align="center" mb={2}>
                      <Icon as={FaInfoCircle} color="#F0B90B" mr={2} />
                      <Text color="#F0B90B" fontWeight="bold">Withdrawal Information</Text>
                    </Flex>

                    <Divider borderColor="#2B3139" mb={3} />

                    <VStack spacing={2} align="stretch">
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">Minimum Withdrawal:</Text>
                        <Text color="#EAECEF" fontSize="sm">
                          {formatAmount(getMinWithdrawal())} {selectedCrypto}
                          {phase3ValidationEnabled && withdrawableBalance && (
                            <Text as="span" color="#F0B90B" fontSize="xs" ml={1}>
                              (Phase 3)
                            </Text>
                          )}
                        </Text>
                      </Flex>
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">Withdrawal Fee:</Text>
                        <Text color="#EAECEF" fontSize="sm">
                          {formatAmount(getWithdrawalFee())} {selectedCrypto}
                        </Text>
                      </Flex>
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">Processing Time:</Text>
                        <Text color="#EAECEF" fontSize="sm">{getProcessingTime()}</Text>
                      </Flex>
                    </VStack>
                  </Box>
                </VStack>

                <Flex justify="flex-end" mt={6}>
                  <Button
                    bg="#F0B90B"
                    color="#0B0E11"
                    _hover={{ bg: "#F8D12F" }}
                    onClick={nextStep}
                    isDisabled={
                      !amount ||
                      parseFloat(amount) <= 0 ||
                      parseFloat(amount) > getAvailableBalance() ||
                      parseFloat(amount) < getMinWithdrawal() ||
                      !selectedNetwork ||
                      getAvailableBalance() <= 0 ||
                      (withdrawalType === 'principal' && !selectedInvestmentId)
                    }
                    w="100%"
                  >
                    Next
                  </Button>
                </Flex>
              </TabPanel>

              {/* Step 2: Wallet Address */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <Box p={4} bg="#1E2329" borderRadius="md">
                    <Text color="#F0B90B" fontSize="md" fontWeight="bold" mb={3}>Wallet Information</Text>

                    {currentNetworkDetails && (
                      <Alert status="info" borderRadius="md" mb={4} bg="#0ECB8122">
                        <AlertIcon color="#0ECB81" />
                        <Box>
                          <Text fontWeight="bold" color="#EAECEF">
                            {t('withdrawModal.selectedNetwork', 'Selected Network')}
                          </Text>
                          <Text fontSize="sm" color="#EAECEF">
                            {currentNetworkDetails.name} - {currentNetworkDetails.description}
                          </Text>
                          <Text fontSize="xs" color="#EAECEF" mt={1}>
                            {t('withdrawModal.processingTime', 'Processing Time')}: {currentNetworkDetails.processingTime}
                          </Text>
                          <Text fontSize="xs" color="#EAECEF">
                            {t('withdrawModal.networkFee', 'Network Fee')}: {currentNetworkDetails.fee} {selectedCrypto}
                          </Text>
                        </Box>
                      </Alert>
                    )}

                    {currentNetworkDetails?.warningMessage && (
                      <Alert status="warning" borderRadius="md" mb={4} bg="#F0B90B22">
                        <AlertIcon color="#F0B90B" />
                        <Box>
                          <Text fontWeight="bold">{t('withdrawModal.networkWarning', 'Important Network Information')}</Text>
                          <Text fontSize="sm">{currentNetworkDetails.warningMessage}</Text>
                        </Box>
                      </Alert>
                    )}

                    {/* Saved Addresses Section */}
                    {loadingAddresses ? (
                      <Box textAlign="center" py={4}>
                        <Text color="#848E9C">Loading saved addresses...</Text>
                      </Box>
                    ) : savedAddresses.length > 0 ? (
                      <FormControl mb={4}>
                        <FormLabel color="#848E9C" fontSize="sm">
                          Saved {selectedCrypto} Addresses
                        </FormLabel>
                        <RadioGroup
                          value={selectedAddressId}
                          onChange={(value) => {
                            setSelectedAddressId(value);
                            if (value === 'new') {
                              setIsNewAddress(true);
                              setWalletAddress('');
                            } else {
                              setIsNewAddress(false);
                              const selectedAddr = savedAddresses.find(addr => addr._id === value);
                              if (selectedAddr) {
                                setWalletAddress(selectedAddr.address);
                              }
                            }
                          }}
                        >
                          <Stack direction="column" spacing={3}>
                            {savedAddresses.map((address) => (
                              <Radio
                                key={address._id}
                                value={address._id}
                                colorScheme="yellow"
                                borderColor="#2B3139"
                              >
                                <VStack align="stretch" w="100%" spacing={1}>
                                  <HStack justify="space-between" w="100%">
                                    <Text color="#EAECEF" fontWeight="bold">{address.label}</Text>
                                    <Badge colorScheme={address.isVerified ? "green" : "yellow"}>
                                      {address.isVerified ? "Verified" : "Unverified"}
                                    </Badge>
                                  </HStack>
                                  <Text color="#848E9C" fontSize="xs" fontFamily="monospace">
                                    {address.address}
                                  </Text>
                                  <Text color="#848E9C" fontSize="xs">
                                    Network: {address.network || 'Default'}
                                  </Text>
                                </VStack>
                              </Radio>
                            ))}
                            <Radio
                              value="new"
                              colorScheme="yellow"
                              borderColor="#2B3139"
                            >
                              <Text color="#F0B90B">+ Add New Address</Text>
                            </Radio>
                          </Stack>
                        </RadioGroup>
                      </FormControl>
                    ) : (
                      <Alert status="info" borderRadius="md" mb={4} bg="#0ECB8122">
                        <AlertIcon color="#0ECB81" />
                        <Box>
                          <Text color="#EAECEF" fontSize="sm">
                            No saved addresses found for {selectedCrypto} on {selectedNetwork} network.
                          </Text>
                          <Text color="#848E9C" fontSize="xs" mt={1}>
                            Enter a new address below and it will be saved for future use.
                          </Text>
                        </Box>
                      </Alert>
                    )}

                    {/* New Address Input - Show when no saved addresses or "Add New" is selected */}
                    {(isNewAddress || savedAddresses.length === 0) && (
                      <>
                        <FormControl isRequired>
                          <FormLabel color="#848E9C" fontSize="sm">
                            {selectedCrypto} Wallet Address
                          </FormLabel>
                          <Input
                            value={walletAddress}
                            onChange={(e) => setWalletAddress(e.target.value)}
                            placeholder="Enter your wallet address"
                            bg="#0B0E11"
                            borderColor="#2B3139"
                            color="#EAECEF"
                            _hover={{ borderColor: "#F0B90B" }}
                            fontFamily="monospace"
                          />
                          <Text color="#848E9C" fontSize="xs" mt={1}>
                            Address accuracy is important. Funds sent to incorrect addresses cannot be recovered.
                          </Text>
                        </FormControl>

                        <FormControl mt={4}>
                          <FormLabel color="#848E9C" fontSize="sm">
                            Address Label (Optional)
                          </FormLabel>
                          <Input
                            value={addressLabel}
                            onChange={(e) => setAddressLabel(e.target.value)}
                            placeholder={`My ${selectedCrypto} Wallet`}
                            bg="#0B0E11"
                            borderColor="#2B3139"
                            color="#EAECEF"
                            _hover={{ borderColor: "#F0B90B" }}
                          />
                          <Text color="#848E9C" fontSize="xs" mt={1}>
                            Give this address a memorable name for future use.
                          </Text>
                        </FormControl>
                      </>
                    )}

                    {(selectedCrypto === 'XRP' || selectedCrypto === 'DOGE') && (
                      <FormControl mt={4}>
                        <FormLabel color="#848E9C" fontSize="sm">
                          Memo / Tag (If Required)
                        </FormLabel>
                        <Input
                          value={memo}
                          onChange={(e) => setMemo(e.target.value)}
                          placeholder="Enter Memo or Tag information"
                          bg="#0B0E11"
                          borderColor="#2B3139"
                          color="#EAECEF"
                          _hover={{ borderColor: "#F0B90B" }}
                        />
                        <Text color="#848E9C" fontSize="xs" mt={1}>
                          * Required for some cryptocurrencies (e.g. XRP)
                        </Text>
                      </FormControl>
                    )}
                  </Box>

                  <Alert status="info" borderRadius="md" bg="#0ECB8122">
                    <AlertIcon color="#0ECB81" />
                    <Box>
                      <AlertTitle color="#0ECB81">Important Information</AlertTitle>
                      <AlertDescription color="#EAECEF" fontSize="sm">
                        Your withdrawal should be processed within 1 day. Please ensure you have entered the correct wallet address.
                      </AlertDescription>
                    </Box>
                  </Alert>
                </VStack>

                <Flex justify="space-between" mt={6}>
                  <Button
                    variant="outline"
                    onClick={prevStep}
                    borderColor="#2B3139"
                    color="#EAECEF"
                    _hover={{ borderColor: "#F0B90B" }}
                    flex="1"
                    mr={2}
                  >
                    Back
                  </Button>
                  <Button
                    bg="#F0B90B"
                    color="#0B0E11"
                    _hover={{ bg: "#F8D12F" }}
                    onClick={nextStep}
                    isDisabled={!walletAddress}
                    isLoading={savingAddress}
                    loadingText="Saving..."
                    flex="1"
                    ml={2}
                  >
                    {isNewAddress && walletAddress ? 'Save & Continue' : 'Next'}
                  </Button>
                </Flex>
              </TabPanel>

              {/* Step 3: Confirmation */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <Box p={4} bg="#1E2329" borderRadius="md">
                    <Text color="#F0B90B" fontSize="md" fontWeight="bold" mb={3}>Transaction Confirmation</Text>

                    <VStack spacing={3} align="stretch">
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">Withdrawal Type:</Text>
                        <Text color="#EAECEF" fontWeight="bold" fontSize="sm">
                          {withdrawalType === 'interest' ? 'Interest Earnings' :
                           withdrawalType === 'commission' ? 'Referral Commission' : 'Main Balance'}
                        </Text>
                      </Flex>
                      {withdrawalType === 'principal' && selectedInvestmentId && (
                        <Flex justify="space-between">
                          <Text color="#848E9C" fontSize="sm">Investment Package:</Text>
                          <Text color="#EAECEF" fontWeight="bold" fontSize="sm">
                            {(() => {
                              const selectedInvestment = availableBalances?.principal?.find(inv => inv.id === selectedInvestmentId) ||
                                (Array.isArray(investmentPackages) ? investmentPackages.find(pkg => pkg._id === selectedInvestmentId) : null);

                              if (selectedInvestment) {
                                const amount = selectedInvestment.amount;
                                const daysSince = 'daysSinceActivation' in selectedInvestment
                                  ? selectedInvestment.daysSinceActivation
                                  : selectedInvestment.activeDays || 0; // Use activeDays from API
                                return `${formatAmount(amount)} ${selectedCrypto} (${daysSince} days old)`;
                              }
                              return 'Selected Package';
                            })()}
                          </Text>
                        </Flex>
                      )}
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">Cryptocurrency:</Text>
                        <Text color="#EAECEF" fontWeight="bold" fontSize="sm">{selectedCrypto}</Text>
                      </Flex>
                      {currentNetworkDetails && (
                        <Flex justify="space-between">
                          <Text color="#848E9C" fontSize="sm">Network:</Text>
                          <Text color="#EAECEF" fontWeight="bold" fontSize="sm">{currentNetworkDetails.name}</Text>
                        </Flex>
                      )}
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">Amount:</Text>
                        <Text color="#EAECEF" fontWeight="bold" fontSize="sm">
                          {formatAmount(parseFloat(amount))} {selectedCrypto}
                        </Text>
                      </Flex>
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">Withdrawal Fee:</Text>
                        <Text color="#EAECEF" fontWeight="bold" fontSize="sm">
                          {formatAmount(getWithdrawalFee())} {selectedCrypto}
                        </Text>
                      </Flex>
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">Net Amount:</Text>
                        <Text color="#0ECB81" fontWeight="bold" fontSize="sm">
                          {formatAmount(parseFloat(amount) - getWithdrawalFee())} {selectedCrypto}
                        </Text>
                      </Flex>
                      <Divider borderColor="#2B3139" my={1} />
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">Wallet Address:</Text>
                        <Text color="#EAECEF" fontWeight="bold" fontFamily="monospace" fontSize="xs" maxW="180px" isTruncated>
                          {walletAddress}
                        </Text>
                      </Flex>
                      {memo && (
                        <Flex justify="space-between">
                          <Text color="#848E9C" fontSize="sm">Memo / Tag:</Text>
                          <Text color="#EAECEF" fontWeight="bold" fontSize="sm">{memo}</Text>
                        </Flex>
                      )}
                      <Divider borderColor="#2B3139" my={1} />
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">Processing Time:</Text>
                        <Text color="#EAECEF" fontWeight="bold" fontSize="sm">{getProcessingTime()}</Text>
                      </Flex>
                    </VStack>
                  </Box>

                  <Alert status="warning" borderRadius="md" bg="#F0B90B22">
                    <AlertIcon color="#F0B90B" />
                    <Box>
                      <AlertDescription color="#EAECEF" fontSize="sm">
                        Withdrawal cannot be cancelled after confirmation. Please ensure all information is correct.
                        {currentNetworkDetails && (
                          <Text fontSize="xs" fontWeight="bold" color="#F0B90B" mt={1}>
                            {t('withdrawModal.networkFinalWarning', `IMPORTANT: This withdrawal will be processed on the ${currentNetworkDetails.name}. Make sure your receiving wallet supports this network.`)}
                          </Text>
                        )}
                      </AlertDescription>
                    </Box>
                  </Alert>
                </VStack>

                <Flex justify="space-between" mt={6}>
                  <Button
                    variant="outline"
                    onClick={prevStep}
                    borderColor="#2B3139"
                    color="#EAECEF"
                    _hover={{ borderColor: "#F0B90B" }}
                    flex="1"
                    mr={2}
                  >
                    Back
                  </Button>
                  <Button
                    bg="#F0B90B"
                    color="#0B0E11"
                    _hover={{ bg: "#F8D12F" }}
                    onClick={handleSubmit}
                    isLoading={isSubmitting}
                    loadingText="Processing..."
                    flex="1"
                    ml={2}
                  >
                    Confirm
                  </Button>
                </Flex>

                {isSubmitting && (
                  <Box mt={4}>
                    <Text fontSize="xs" color="#848E9C" mb={2}>
                      Processing your transaction...
                    </Text>
                    <Progress value={uploadProgress} size="xs" colorScheme="yellow" borderRadius="full" />
                  </Box>
                )}
              </TabPanel>
            </TabPanels>
          </Tabs>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default WithdrawModal;
