import mongoose, { Document, Schema, Model } from 'mongoose';

// Interface for address with network
export interface AddressWithNetwork {
  address: string;
  network: string;
}

// Interface for cryptocurrency address configuration
export interface CryptoAddressConfig {
  currency: string;
  addresses: string[] | AddressWithNetwork[]; // Hỗ trợ cả cấu trúc cũ và mới
  currentIndex: number;
  enabled: boolean;
}

// Interface for system configuration
export interface ISystemConfig extends Document {
  siteName: string;
  siteDescription: string;
  maintenanceMode: boolean;
  maintenanceMessage?: string;
  commissionRate: number;
  referralRate: number;
  minimumDeposit: number;
  minimumWithdrawal: number;
  withdrawalsEnabled: boolean;
  depositsEnabled: boolean;
  emailNotifications?: string;
  cryptoAddresses: CryptoAddressConfig[];
  supportedCurrencies: string[];
  createdAt: Date;
  updatedAt: Date;
}

// Interface for SystemConfig model with static methods
export interface ISystemConfigModel extends Model<ISystemConfig> {
  findOneOrCreate(): Promise<ISystemConfig>;
}

// Schema for system configuration
const systemConfigSchema = new Schema<ISystemConfig>(
  {
    siteName: {
      type: String,
      required: true,
      default: 'Shipping Finance',
      trim: true,
    },
    siteDescription: {
      type: String,
      required: true,
      default: 'Secure Crypto Investment Platform',
      trim: true,
    },
    maintenanceMode: {
      type: Boolean,
      default: false,
    },
    maintenanceMessage: {
      type: String,
      trim: true,
      default: '',
    },
    commissionRate: {
      type: Number,
      required: true,
      default: 1.0,
      min: 0,
      max: 100,
    },
    referralRate: {
      type: Number,
      required: true,
      default: 3.0,
      min: 0,
      max: 100,
    },
    minimumDeposit: {
      type: Number,
      required: true,
      default: 100,
      min: 0,
    },
    minimumWithdrawal: {
      type: Number,
      required: true,
      default: 20,
      min: 0,
    },
    withdrawalsEnabled: {
      type: Boolean,
      default: true,
    },
    depositsEnabled: {
      type: Boolean,
      default: true,
    },
    emailNotifications: {
      type: String,
      enum: ['all', 'deposits', 'withdrawals', 'none'],
      default: 'all',
    },
    cryptoAddresses: [
      {
        currency: {
          type: String,
          required: true,
          trim: true,
          uppercase: true,
        },
        addresses: [
          {
            type: Schema.Types.Mixed, // Sử dụng Mixed type để hỗ trợ cả string và object
            required: true,
          },
        ],
        currentIndex: {
          type: Number,
          default: 0,
          min: 0,
        },
        enabled: {
          type: Boolean,
          default: true,
        },
        network: {
          type: String,
          trim: true,
        },
      },
    ],
    supportedCurrencies: [
      {
        type: String,
        trim: true,
        uppercase: true,
      },
    ],
  },
  {
    timestamps: true,
  }
);

// Create a singleton pattern to ensure only one system config exists
systemConfigSchema.statics.findOneOrCreate = async function (): Promise<ISystemConfig> {
  const config = await this.findOne().exec();
  if (config) {
    return config;
  }

  // Create default configuration with initial crypto addresses
  return this.create({
    siteName: 'Shipping Finance',
    siteDescription: 'Secure Crypto Investment Platform',
    commissionRate: 1.0,
    referralRate: 3.0,
    minimumDeposit: 100,
    minimumWithdrawal: 50,
    cryptoAddresses: [
      {
        currency: 'BTC',
        addresses: [
          '**********************************',
          '**********************************',
          '**********************************',
        ],
        currentIndex: 0,
        enabled: true,
      },
      {
        currency: 'ETH',
        addresses: [
          '******************************************',
          '******************************************',
          '******************************************',
        ],
        currentIndex: 0,
        enabled: true,
      },
      {
        currency: 'USDT',
        addresses: [
          'TKQvCcXdgmAQwu4U5RgQJdFfvGbmzXxC9X',
          'TUrMmF9Gd4rzrXsQ34ui3Wou94E7HFuJQh',
          'TLsV52sRDL79HXGGm9yzwKibb6BeruhUzy',
        ],
        currentIndex: 0,
        enabled: true,
      },
      {
        currency: 'DOGE',
        addresses: [
          'DH5yaieqoZN36fDVciNyRueRGvGLR3mr7L',
          'DQA3bK3VDWB7QQQhSrD5YQQQhSrD5YQQQ',
          'D7P2gZQQQhSrD5YQQQhSrD5YQQQhSrD5Y',
        ],
        currentIndex: 0,
        enabled: true,
      },
      {
        currency: 'TRX',
        addresses: [
          'TLPuNinqS5qHuVMHWadqA7RZ2LcxdjCWzb',
          'TKWLzPKNdgzVwYbSYnFVBcL1uEE9CfTQbX',
          'TUrMmF9Gd4rzrXsQ34ui3Wou94E7HFuJQh',
        ],
        currentIndex: 0,
        enabled: true,
      },
    ],
    supportedCurrencies: ['BTC', 'ETH', 'USDT', 'DOGE', 'TRX'],
  });
};

const SystemConfig = mongoose.models.SystemConfig || mongoose.model<ISystemConfig, ISystemConfigModel>('SystemConfig', systemConfigSchema);

export default SystemConfig;
