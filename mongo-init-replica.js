// MongoDB Replica Set Initialization Script
// This script initializes a single-node replica set to enable transactions
// with keyFile authentication

print('Starting MongoDB replica set initialization with keyFile authentication...');

// Wait for MongoDB to be ready
var attempts = 0;
var maxAttempts = 30;

while (attempts < maxAttempts) {
  try {
    // Try to connect and check if we can run commands
    var status = db.adminCommand({ ping: 1 });
    if (status.ok === 1) {
      print('MongoDB is ready, proceeding with replica set initialization...');
      break;
    }
  } catch (e) {
    print('Waiting for MongoDB to be ready... Attempt ' + (attempts + 1) + '/' + maxAttempts);
    sleep(2000); // Wait 2 seconds
    attempts++;
  }
}

if (attempts >= maxAttempts) {
  print('ERROR: MongoDB did not become ready within the expected time');
  quit(1);
}

try {
  // Check if replica set is already initialized
  var rsStatus;
  try {
    rsStatus = rs.status();
    print('Replica set already initialized with status: ' + rsStatus.ok);

    if (rsStatus.ok === 1) {
      print('Replica set is already running and healthy');
      quit(0);
    }
  } catch (e) {
    print('Replica set not yet initialized, proceeding with initialization...');
  }

  // Initialize replica set
  var config = {
    _id: 'rs0',
    members: [
      {
        _id: 0,
        host: 'mongodb-dev:27017',
        priority: 1
      }
    ]
  };

  print('Initializing replica set with config:');
  printjson(config);

  var result = rs.initiate(config);
  print('Replica set initiation result:');
  printjson(result);

  if (result.ok === 1) {
    print('Replica set initialized successfully!');

    // Wait for the replica set to become primary
    var waitAttempts = 0;
    var maxWaitAttempts = 30;

    while (waitAttempts < maxWaitAttempts) {
      try {
        var status = rs.status();
        if (status.members && status.members[0] && status.members[0].stateStr === 'PRIMARY') {
          print('Replica set member is now PRIMARY');
          break;
        }
        print('Waiting for replica set member to become PRIMARY... Attempt ' + (waitAttempts + 1) + '/' + maxWaitAttempts);
        sleep(2000);
        waitAttempts++;
      } catch (e) {
        print('Error checking replica set status: ' + e);
        sleep(2000);
        waitAttempts++;
      }
    }

    if (waitAttempts >= maxWaitAttempts) {
      print('WARNING: Replica set member did not become PRIMARY within expected time');
    } else {
      print('Replica set is ready and transactions are now supported!');
    }

  } else {
    print('ERROR: Failed to initialize replica set');
    printjson(result);
    quit(1);
  }

} catch (e) {
  print('ERROR during replica set initialization: ' + e);
  quit(1);
}

print('MongoDB replica set initialization completed successfully!');
